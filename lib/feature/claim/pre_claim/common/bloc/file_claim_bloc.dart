import 'dart:convert';
import 'dart:io';

import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/bottomsheet/BottomSheet.dart';
import 'package:acko_flutter/feature/claim/advance_cash/onboarding/enums/advance_cash_screens.dart';
import 'package:acko_flutter/feature/claim/advance_cash/onboarding/view/advance_cash_status_page.dart';
import 'package:acko_flutter/feature/claim/my_claims_home/bloc/my_claims_bloc.dart';
import 'package:acko_flutter/feature/claim/post_claim/common/bloc/claim_success_cubit.dart';
import 'package:acko_flutter/feature/claim/post_claim/common/models/claim_status_model.dart';
import 'package:acko_flutter/feature/claim/post_claim/common/view/claim_success_page.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/bloc/file_claim_stream.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/models/bank_details.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/models/claim_entities.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/models/doc_upload_entity.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/models/treatment_entity.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/repository/file_claim_repository.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/view/claims_declaration_page.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/view/file_claim.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/view/selfie_page.dart';
import 'package:acko_flutter/feature/payment/payment_constants.dart';
import 'package:acko_flutter/feature/payment/payout/models/payout_models.dart';
import 'package:acko_flutter/feature/recurring_payment/recurring_payment_models.dart';
import 'package:acko_flutter/network/ApiResponse.dart';
import 'package:acko_flutter/r2d2/model/data.dart';
import 'package:policy_details/policy_details.dart';
import 'package:utilities/constants/constants.dart';
import 'package:acko_flutter/util/Stack.dart';
import 'package:acko_flutter/util/TrackerManager.dart';
import 'package:acko_flutter/util/events_constants.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:acko_flutter/util/health/page_event_listener.dart';
import 'package:acko_flutter/util/health/utils.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/health_life/page_events/health_life_page_events.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';
import 'package:networking_module/util/file_progress_callback.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:sprintf/sprintf.dart';

import '../../../../../common/view/router.dart';
import '../../../../payment/payout/acko_payout_constants.dart';
import '../../../my_claims_home/model/my_claims_models.dart';
import '../models/fnol_cover_page_entity.dart';
import '../models/health_policies.dart';

class FileClaimBloc extends AckoSafeCubit<FileClaimState> {
  ClaimRequest? claimRequest;
  DeficiencyInfo? deficiencyInfo;
  bool saveState = false;
  final bool? loadFromDraft;
  bool isDocumentProceedTapped = false;
  bool isEditing = false;
  bool isAdditional = false;
  bool resetDocsFlag = false;
  final bool isAdvanceCashFlow;
  final bool hasPendingPayment;
  bool isAlertShownForACRClaim = false;
  bool isAnyDocMandatory = false;
  bool isClaimForAdvanceCash;
  late final FileClaimRepository fileClaimRepository;
  Future<TreatmentResponse>? treatmentFuture;
  CancelToken? treatmentSearchToken;
  TreatmentResponse? treatmentResponse;
  int? acrId;

  FNOLCoverPageResponse? coverPageDataResponse;
  ClaimHealthPolicies? fnolHealthPolicies;
  bool seeAllCategoryLoaded = false;
  PolicyType policyType = PolicyType.ACTIVE;

  int totalPrePostClaimsPages = 0;
  int prePostPagesCount = 1;

  int? get claimId => (claimRequest != null && claimRequest!.claimId != null)
      ? claimRequest!.claimId
      : deficiencyInfo!.claimId != null
          ? deficiencyInfo!.claimId
          : null;

  bool get isIPD =>
      claimRequest?.memberSelection?.selectedCover?.categoryType
          ?.toLowerCase() ==
      "ipd";

  String get acknowledgeText => sprintf(kyc_authorise, [
        claimRequest?.memberSelection?.proposer?.name ?? '',
        claimRequest?.memberSelection?.payoutMember?.name ??
            claimRequest?.memberSelection?.selectedMember?.name ??
            ''
      ]);

  bool get isKYCApplicable =>
      (claimRequest?.memberSelection?.proposer?.name?.toLowerCase() !=
          claimRequest?.memberSelection?.payoutMember?.name?.toLowerCase()) ||
      (claimRequest?.selectedHealthPlanPolicy?.productType?.toLowerCase() ==
              HealthConstants.RETAIL_PRODUCT ||
          claimRequest?.memberSelection?.payoutMember?.policies?.first
                  .productType ==
              HealthConstants.RETAIL_PRODUCT);

  bool get isAcknowledgementNecessary =>
      claimRequest?.memberSelection?.proposer?.name?.toLowerCase() !=
      claimRequest?.memberSelection?.payoutMember?.name?.toLowerCase();

  bool get toGetConsent =>
      !(claimRequest?.selectedHealthPlanPolicy?.productType?.toLowerCase() ==
          HealthConstants.RETAIL_PRODUCT) &&
      claimRequest?.memberSelection?.proposer?.name?.toLowerCase() ==
          claimRequest?.memberSelection?.payoutMember?.name?.toLowerCase();

  bool? _submitClaimClickEnabled = true;

  FileClaimBloc(
      {this.loadFromDraft = false,
      this.deficiencyInfo,
      this.isAdvanceCashFlow = false,
      String? incidentReason,
      this.acrId,
      this.hasPendingPayment = false,
      this.isClaimForAdvanceCash = false})
      : super(LoadingFileClaimState(false)) {
    fileClaimRepository = FileClaimRepository(
        isAdvanceCashFlow: this.isAdvanceCashFlow,
        isClaimForAdvanceCash: this.isClaimForAdvanceCash);
    claimRequest = ClaimRequest()..incidentReason = incidentReason;
  }

  void emitState(FileClaimState state) {
    if (!isClosed) emit(state);
  }

  void _loadInitialPage() async {
    if (isClaimForAdvanceCash && acrId != null) {
      emitState(ShowLoader(true));
      claimRequest = await fileClaimRepository.getClaimRequestFromAdvCash(
          acrId!, claimRequest);
      String? error = await getDocumentConfig();
      emitState(ShowLoader(false));
      if (error == null) {
        loadFNOLScreen(FNOLPage.DocUpload);
        Map<int, DSStack> map = {
          FNOLPage.DocUpload.index: DSStack<int>()
            ..push(FNOLPage.DocUpload.index)
        };
        emitState(LoadedFileClaimDataState(map.keys.first, map.values.first));
      }
    } else {
      Map<int, DSStack> map = _getInitialPageFromClaimRequest();
      emitState(LoadedFileClaimDataState(map.keys.first, map.values.first));
    }
  }

  void loadFNOLScreen(FNOLPage pageIndex,
      {bool? clearTop,
      bool animate = true,
      NavigationDirection navigationDirection = NavigationDirection.FORWARD}) {
    emitState(ChangePageState(pageIndex.index,
        clearTop == null ? false : clearTop, navigationDirection));
  }

  Map<int, DSStack<int>> _getInitialPageFromClaimRequest() {
    DSStack<int> dsStack = DSStack();
    dsStack.push(FNOLPage.MedicalVisit.index);
    if (claimRequest?.claimAmount != null &&
        claimRequest?.memberSelection?.selectedMember == null) {
      dsStack.push(FNOLPage.MemberSelection.index);
    }
    if (claimRequest?.claimAmount != null &&
        claimRequest?.memberSelection?.selectedMember != null &&
        claimRequest?.memberSelection?.selectedCover == null) {
      dsStack.push(FNOLPage.MemberSelection.index);
      dsStack.push(FNOLPage.FNOLCoverPage.index);
    } else {
      if (claimRequest?.claimAmount != null &&
          claimRequest?.memberSelection?.selectedMember != null &&
          claimRequest?.memberSelection?.selectedCover != null) {
        dsStack.push(FNOLPage.MemberSelection.index);
        dsStack.push(FNOLPage.FNOLCoverPage.index);
        if (claimRequest?.prePostClaimantUhid == null &&
            claimRequest?.treatment == null) {
          dsStack.push(_getPageIndexPostTreatmentSelection());
        } else {
          if (FNOLPage.HealthPolicyPlanPage.index ==
                  _getPageIndexPostTreatmentSelection() &&
              claimRequest?.selectedHealthPlanPolicy == null) {
            dsStack.push(FNOLPage.HealthPolicyPlanPage.index);
          } else {
            if (claimRequest?.treatment != null)
              dsStack.push(FNOLPage.TreatmentSearchPage.index);
            dsStack.push(_getPageIndexPostTreatmentSelection());
            if (!hasPendingPayment) {
              dsStack.push(FNOLPage.DocUpload.index);
              Map<String, int> err = _verifyDocumentsAndGetDeficiencyPage();
              if (err.values.first != FNOLPage.DocUpload.index) {
                dsStack.push(FNOLPage.PayoutMember.index);
                if (claimRequest?.memberSelection?.payoutMember != null) {
                  if (isKYCApplicable) {
                    if (err.values.first >= FNOLPage.PayoutMember.index ||
                        err.values.first == 0) {
                      if (claimRequest?.kycDetails
                              ?.validateKYCPage(KYCState.DOC) ==
                          null) {
                        dsStack.push(FNOLPage.KYCPage.index);
                        if (claimRequest?.kycDetails
                                ?.validateKYCPage(KYCState.SELFIE) ==
                            null) {
                          if (claimRequest!.disclaimerCompleted == true) {
                            dsStack.push(FNOLPage.ClaimReviewPage.index);
                          }
                        }
                      }
                    } else {
                      if (claimRequest?.memberSelection?.payoutMember != null &&
                          claimRequest?.payoutConsent == true &&
                          claimRequest?.kycDetails?.panCard != null) {
                        if ((err.values.first == FNOLPage.KYCPage.index &&
                                isKYCApplicable) ||
                            isKYCApplicable)
                          dsStack.push(FNOLPage.KYCPage.index);
                      } else {
                        dsStack.push(FNOLPage.PayoutMember.index);
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    return {dsStack.top()!: dsStack};
  }

  /********************************************************************
   ****************START OF MEMBER AND COVER SELECTION************************
   ********************************************************************/

  void getDependentsList() async {
    try {
      if (loadFromDraft == true) {
        final reason = claimRequest?.incidentReason;
        claimRequest = await _getDraftState();
        if (claimRequest != null && claimRequest?.incidentReason != reason)
          fileClaimRepository.getFNOLCoverPageData(claimRequest!);
        // if (await getBoolPrefs(
        //         BoolDataSharedPreferenceKeys.HEALTH_CLAIM_DRAFT_CLEANER) !=
        //     true) {
        //   setBoolPrefs(
        //       BoolDataSharedPreferenceKeys.HEALTH_CLAIM_DRAFT_CLEANER, true);
        //   await discardDraft();
        // } else {
        //
        // }
      }
      if (claimRequest?.memberSelection == null)
        claimRequest?.memberSelection =
            await fileClaimRepository.getMemberSelection();

      _loadInitialPage();
    } catch (ex, stack) {
      emitState(LoadingFileClaimState(false));
      debugPrint(ex.toString());
      debugPrintStack(stackTrace: stack);
    }
  }

  //load members for selected date of claim
  Future<String?> setPolicyMembersData(DateTime picked) async {
    if (claimRequest?.date != picked) {
      if (isClaimForAdvanceCash && _showAdvanceCashPopup(selectedDate: picked))
        return Future.value("");
      else
        claimRequest?.date = picked;
    }
    try {
      emitState(ShowLoader(true));
      claimRequest?.memberSelection?.policiesForDate(
          await fileClaimRepository.getPoliciesForADate(picked),
          isAdvanceCashFlow);
      emitState(ShowLoader(false));
      if (claimRequest?.memberSelection?.policiesForSelectedPeriod?.isEmpty ??
          true)
        return Future.value(no_member_date_error);
      else
        return Future.value(null);
    } catch (ex) {
      emitState(ShowLoader(false));
      return Future.value(ex.toString());
    }
  }

  //show advance cash ongoing popup if the selected date is same as of advance cash date
  bool _showAdvanceCashPopup({DateTime? selectedDate, String? memberUhid}) {
    if (isClaimForAdvanceCash &&
        (selectedDate != null &&
                !selectedDate
                    .isAtSameMomentAs(claimRequest?.date ?? DateTime.now()) ||
            (memberUhid != null &&
                claimRequest?.memberSelection?.selectedMember?.policies !=
                    null &&
                !claimRequest!.memberSelection!.selectedMember!.policies!.any(
                    (policy) =>
                        policy.insureds
                            ?.any((insured) => insured.uhid == memberUhid) ??
                        false)))) {
      emitState(ShowAdvanceCashClaim(
          memberName:
              claimRequest?.memberSelection?.selectedMember?.name ?? "proposer",
          message: selectedDate != null
              ? acr_existent_for_date
              : memberUhid != null
                  ? acr_existent_for_member
                  : "",
          disabledCTA: selectedDate != null
              ? change_date
              : memberUhid != null
                  ? change_member
                  : ""));
      return true;
    } else
      return false;
  }

  void onPatientSelected(BuildContext context, int _indexSelected) async {
    if (isClaimForAdvanceCash &&
        _showAdvanceCashPopup(
            memberUhid: claimRequest
                ?.memberSelection
                ?.policiesForSelectedPeriod?[_indexSelected]
                ?.policies
                ?.first
                .insureds
                ?.where((insured) => insured.uhid != null)
                .firstOrNull
                ?.uhid)) {
      return;
    }
    claimRequest?.selectedMemberIndex = _indexSelected;
    final selectedUser = claimRequest
        ?.memberSelection?.policiesForSelectedPeriod?[_indexSelected];
    if (selectedUser?.policies?.isNotEmpty ?? false) {
      claimRequest?.memberSelection?.selectedMember = selectedUser;
      try {
        if (isAdvanceCashFlow) {
          try {
            claimRequest =
                await fileClaimRepository.createUpdateACR(claimRequest);
            removeAllUploadedDocs(FNOLPage.DocUpload);
            moveToDocUploadPage();
          } catch (ex) {
            UiUtils.getInstance.showToast(ex.toString());
          }
        } else {
          AnalyticsTrackerManager.instance
              .sendEvent(event: HLTrackEvents.TAP_MEMBER_NAME, properties: {
            "policy_status": selectedUser?.policies![0].policyStatus,
            "uhid": getUhidForInsured(selectedUser),
          });
          claimRequest?.memberSelection?.evaluateProposer(
              productType: selectedUser?.policies?.first.productType);
          loadFNOLScreen(FNOLPage.FNOLCoverPage);
        }
      } catch (e) {
        UiUtils.getInstance.showToast('some error');
      }
    } else {
      UiUtils.getInstance.showToast('no policy available');
    }
  }

  String? getUhidForInsured(HealthAssetDetailsResponseInsured? selectedUser) {
    String? uhid;
    selectedUser?.policies?.forEach((policy) {
      policy.insureds?.forEach((insured) {
        if (insured.name.equalsIgnoreCase(selectedUser.name)) {
          uhid = insured.uhid;
        }
      });
    });
    return uhid;
  }

  void loadFNOLCoverPageData() async {
    emitState(LoadingFNOLDataState());
    try {
      coverPageDataResponse =
          await fileClaimRepository.getFNOLCoverPageData(claimRequest!);

      if (coverPageDataResponse == null) throw "some error";
      if (coverPageDataResponse?.error == null) {
        if (coverPageDataResponse?.acrId != null) {
          claimRequest = await fileClaimRepository.getClaimRequestFromAdvCash(
              coverPageDataResponse!.acrId!, claimRequest);
          if (!isAlertShownForACRClaim) {
            if (!isClaimForAdvanceCash)
              removeAllUploadedDocs(FNOLPage.DocUpload);
            isClaimForAdvanceCash = true;
            fileClaimRepository.isClaimForAdvanceCash = true;
            // await setBoolPrefs(SharedPreferencesKeys.AC_MEMBER_SELECTED_FOR_CLAIM, true);
            emitState(ShowAdvanceCashClaim(
                memberName:
                    claimRequest?.memberSelection?.selectedMember?.name ??
                        "proposer"));
          } else {
            isClaimForAdvanceCash = false;
            moveToDocUploadPage();
          }
        } else {
          emitState(LoadedFNOLCoverPageData(coverPageDataResponse));
        }
      } else {
        emitState(ErrorFNOLCoverPage(coverPageDataResponse?.error));
      }
    } catch (ex, stack) {
      debugPrintStack(stackTrace: stack);
      emitState(ErrorFNOLCoverPage(ErrorHandler(DioExceptionType.cancel)));
    }
  }

  void onCategoryCoverSelected(
      BuildContext context, CategoryCovers selectedCategoryCover,
      {bool loadNextPage = true}) async {
    try {
      if (claimRequest?.memberSelection?.selectedMember?.policies?.isNotEmpty ??
          false) {
        if (claimRequest?.memberSelection?.selectedCover?.coverKey !=
            selectedCategoryCover.coverKey) {
          claimRequest?.prePostClaimId = null;
          claimRequest?.prePostClaimantUhid = null;
          claimRequest?.prePostSubClaimId = null;
          claimRequest?.selectedHealthPlanPolicy = null;
          claimRequest?.memberSelection?.selectedCover = selectedCategoryCover;
        }

        final error = await updateClaim();
        if (error == null) {
          if (loadNextPage) {
            if (selectedCategoryCover.categoryType == "IPD" &&
                selectedCategoryCover.coverKey != "pre_post_hospitalisation") {
              loadFNOLScreen(FNOLPage.TreatmentSearchPage);
            } else if (selectedCategoryCover.coverKey ==
                "pre_post_hospitalisation") {
              loadFNOLScreen(FNOLPage.PrePostHospitalization);
            } else {
              String? error = await getDocumentConfig();
              if (error == null) loadFNOLScreen(FNOLPage.DocUpload);
            }
          }
        } else {
          if ((error.errorMessage ?? '').contains('policy start-end date') &&
              error.statusCode == 400) {
            claimRequest?.selectedMemberIndex = null;
            claimRequest?.memberSelection?.selectedMember = null;
            emit(
              ErrorFNOLDateLapsed(
                error,
              ),
            );
          } else {
            emit(
              ErrorFNOLCoverPage(
                error,
              ),
            );
          }
        }
      } else {
        UiUtils.getInstance.showToast('no policy available');
      }
    } catch (e) {
      UiUtils.getInstance.showToast('some error');
    }
  }

  Future<dynamic> handleDuplicateClaimNavigation(
      Cta? cta, BuildContext context) async {
    switch (cta?.pageId) {
      case "claim_detail":
        AnalyticsTrackerManager.instance.sendEvent(
            event: HLTrackEvents.TAP_TRACK_CLAIM,
            properties: {"page": "duplicate_claim_modal"});
        discardDraft();
        if (cta?.urls != null) {
          return Future.value(() =>Navigator.of(context)
                  .pushReplacementNamed(Routes.IPD_TRACK_STATUS_PAGE, arguments: {
                'claimsUrl': cta?.urls,
              }));
        }
        break;
      case "claim_list":
        final (raiseClaimUrl, viewClaimUrl, isEducationEnabled) = await getClaimUrlsAndFlag();
        if(isEducationEnabled) {
          Navigator.pushNamed(context, Routes.WEB_PAGE_V2,
              arguments: {'url': "$viewClaimUrl"});
        } else {
          AnalyticsTrackerManager.instance.sendEvent(
              event: HLTrackEvents.TAP_CLAIMS,
              properties: {"page": "duplicate_claim_modal"});
          discardDraft();
          SchedulerBinding.instance.addPostFrameCallback(
                (_) {
              Navigator.pushNamedAndRemoveUntil(
                context,
                Routes.HEALTH_CLAIMS_LIST_PAGE,
                ModalRoute.withName(
                  Routes.APP_HOME,
                ),
              );
            },
          );
        }
        return Future.value(true);
      case "treatment_search":
        AnalyticsTrackerManager.instance.sendEvent(
            event: HLTrackEvents.TAP_CONTINUE_CLAIM_REGISTRATION,
            properties: {"page": "duplicate_claim_modal"});
        loadFNOLScreen(FNOLPage.TreatmentSearchPage);
        return Future.value(true);
      case "pre_post_hospitalisation":
        AnalyticsTrackerManager.instance.sendEvent(
            event: HLTrackEvents.TAP_REGISTER_PRE_POST,
            properties: {"page": "duplicate_claim_modal"});
        loadFNOLScreen(FNOLPage.PrePostHospitalization);
        return Future.value(true);
    }
    return Future.value(null);
  }

  int _getPageIndexPostTreatmentSelection() {
    if (isIPD &&
        claimRequest?.memberSelection?.selectedCover?.coverKey !=
            HealthConstants.PREPOSTSTRING) {
      if (claimRequest?.treatment != null) {
        return FNOLPage.HealthPolicyPlanPage.index;
      }
      return FNOLPage.TreatmentSearchPage.index;
    } else {
      if (claimRequest?.memberSelection?.selectedCover?.coverKey ==
          HealthConstants.PREPOSTSTRING) {
        debugPrint("PrePostHospitalization");
        return FNOLPage.PrePostHospitalization.index;
      } else {
        return FNOLPage.DocUpload.index;
      }
    }
  }


  Future<(String, String, bool)> getClaimUrlsAndFlag() async {
    final jsonData = await RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.CLAIMS_ENTRY_CONFIG);

    // Default values
    final defaultRaiseClaimUrl =
        '${Constants.BASE_URL}gi/p/health/claim/raise-claim?hide_app_bar=true';
    final defaultViewClaimUrl =
        '${Constants.BASE_URL}gi/p/health/claim/view-claims?hide_app_bar=true';
    final defaultEducationEnabled = false;

    if (jsonData == null || jsonData is! Map<String, dynamic>) {
      return (defaultRaiseClaimUrl, defaultViewClaimUrl, defaultEducationEnabled);
    }

    final assetRaiseClaim = jsonData['asset_raise_claim'];
    final assetViewClaim = jsonData['asset_view_claim'];
    final claimsEducationEnabled = jsonData['claims_education_enabled'];

    final raiseClaimUrl = (assetRaiseClaim is String && assetRaiseClaim.isNotEmpty)
        ? assetRaiseClaim
        : defaultRaiseClaimUrl;

    final viewClaimUrl = (assetViewClaim is String && assetViewClaim.isNotEmpty)
        ? assetViewClaim
        : defaultViewClaimUrl;

    final educationEnabled = claimsEducationEnabled is bool
        ? claimsEducationEnabled
        : defaultEducationEnabled;

    return (raiseClaimUrl, viewClaimUrl, educationEnabled);
  }


  /********************************************************************
   ****************END OF MEMBER AND COVER SELECTION************************
   ********************************************************************/

  /********************************************************************
   ****************START OF TREATMENT SELECTION************************
   ********************************************************************/
  void loadCategoryTreatmentChooser(BuildContext context) async {
    dynamic treatment = await Navigator.of(context)
        .pushNamed(Routes.TREATMENT_CATEGORIES, arguments: {
      'memberName': claimRequest?.memberSelection?.selectedMember?.name,
      'claimId': claimRequest?.claimId ?? 0
    });
    if (treatment != null && treatment is Treatment) {
      seeAllCategoryLoaded = true;
      onTreatmentSelection(context, treatment);
    } else {
      seeAllCategoryLoaded = false;
    }
  }

  void searchTreatment(String value) async {
    emitState(LoadingSearchResults(true));
    if (value.isEmpty) {
      if (treatmentResponse != null)
        return emitState(LoadDefaultSearchOptions(treatmentResponse!));
    }
    treatmentSearchToken?.cancel();
    treatmentSearchToken = CancelToken();
    TreatmentResponse res =
        await fileClaimRepository.searchTreatment(value, treatmentSearchToken);
    if (res.error != null) {
    } else {
      if (value.isEmpty) {
        treatmentResponse = res;
        emitState(LoadDefaultSearchOptions(treatmentResponse!));
      } else
        emitState(LoadingSearchResults(false, treatmentRes: res));
    }
  }

  void onTreatmentSelection(BuildContext context, Treatment treatment) async {
    claimRequest?.treatment = treatment;
    final error = await updateClaim();
    if (error == null)
      loadFNOLScreen(FNOLPage.HealthPolicyPlanPage);
    else
      UiUtils.getInstance.showSnackBar(error.errorMessage ?? '', context);
  }

  /********************************************************************
   ****************END OF TREATMENT SELECTION************************
   ********************************************************************/

  /********************************************************************
   ****************START OF PLAN SELECTION************************
   ********************************************************************/

  void getHealthPolicyPlans() async {
    emitState(LoadingHealthPolicyPlans());
    fnolHealthPolicies =
        await fileClaimRepository.getHealthPoliciesFromServer(claimRequest!);

    if (fnolHealthPolicies?.error == null) {
      if ((fnolHealthPolicies?.covered?.policies?.length ?? 0) > 0) {
        if (claimRequest?.selectedHealthPlanPolicy != null) {
          fnolHealthPolicies?.covered?.policies?.forEach((element) {
            if (element.planId ==
                claimRequest?.selectedHealthPlanPolicy?.planId) {
              element.selected = true;
            } else
              element.selected = false;
          });
        }
        if (claimRequest?.selectedHealthPlanPolicy == null) {
          var policy = fnolHealthPolicies?.covered?.policies?.firstWhere(
              (element) =>
                  element.isDisabled == false && element.isRecommended == true,
              orElse: () => fnolHealthPolicies!.covered!.policies!.first);
          if (policy?.isDisabled == false) selectedHealthPolicyPlan(policy);
        } else {
          final selectedPolicy = fnolHealthPolicies?.covered?.policies
                  ?.where((element) =>
                      element.planId ==
                      claimRequest?.selectedHealthPlanPolicy?.planId)
                  .firstOrNull ??
              fnolHealthPolicies?.covered?.policies?.first;
          selectedHealthPolicyPlan(selectedPolicy);
        }
      }
      emitState(LoadedHealthPolicyPlans());
    } else {
      emitState(LoadedHealthPoliciesError(fnolHealthPolicies?.error));
    }
  }

  void onHealthPolicyPlanSelection(BuildContext context) async {
    claimRequest?.memberSelection?.evaluateProposer(
        proposerId: claimRequest?.selectedHealthPlanPolicy?.proposerUhid);

    final Map<String?, dynamic> map = jsonDecode(await getStringPrefs(
            StringDataSharedPreferenceKeys.PRODUCT_EMAIL_MAP) ??
        '{}');
    String? email = map[claimRequest?.selectedHealthPlanPolicy?.productType];
    map.keys.forEach((key) {
      if (email == null ||
          key?.toLowerCase() == HealthConstants.ENTERPRISE_PRODUCT)
        email = map[key];
    });
    claimRequest?.email = email.isNotNullOrEmpty ? email : null;

    final error = await updateClaim();
    if (error == null) {
      moveToDocUploadPage();
    } else
      UiUtils.getInstance.showSnackBar(error.errorMessage ?? '', context);
  }

  void selectedHealthPolicyPlan(HealthPlanPolicies? healthPlanPolicies) {
    claimRequest?.selectedHealthPlanPolicy?.selected = false;
    healthPlanPolicies?.selected = true;
    claimRequest?.selectedHealthPlanPolicy = healthPlanPolicies;
  }

  void loadPrePostHospitalizationClaims(bool isFirstFetch) async {
    if (isFirstFetch) prePostPagesCount = 1;

    if (!isFirstFetch && (totalPrePostClaimsPages + 1) == prePostPagesCount)
      return;
    if (state is PrePostClaimsLoading) return;

    final currentState = state;

    List<Claims>? oldOngoingClaims = [];
    List<Claims>? oldCompletedClaims = [];

    if (currentState is PrePostClaimsLoaded) {
      oldOngoingClaims = currentState.ongoingClaims;
      oldCompletedClaims = currentState.completedClaims;
    }

    emit(PrePostClaimsLoading(oldOngoingClaims, oldCompletedClaims,
        isFirstFetch: (prePostPagesCount == 1)));

    List<String?>? uhids = List.empty(growable: true);
    final selectedMemberName =
        claimRequest?.memberSelection?.selectedMember?.name ?? '';
    claimRequest?.memberSelection?.selectedMember?.policies?.forEach((element) {
      if (!(element.isAarogyaSanjeevaniPolicy)) {
        element.insureds?.forEach((insured) {
          if (insured.name.equalsIgnoreCase(selectedMemberName) &&
              !uhids.contains(insured.uhid)) {
            uhids.add(insured.uhid);
          }
        });
      }
    });
    ClaimCards? claimCards =
        await fileClaimRepository.getClaimCards(uhids, prePostPagesCount);
    if (claimCards?.error == null) {
      prePostPagesCount++;
      final List<Claims>? ongoingClaims = state is PrePostClaimsLoading
          ? (state as PrePostClaimsLoading).oldOngoingClaims
          : <Claims>[];
      final List<Claims>? completedClaims = state is PrePostClaimsLoading
          ? (state as PrePostClaimsLoading).oldCompletedClaims
          : <Claims>[];

      ongoingClaims?.addAll(claimCards?.ongoingClaims as List<Claims>);
      completedClaims?.addAll(claimCards?.completedClaims as List<Claims>);
      totalPrePostClaimsPages = claimCards?.totalPageCount ?? 0;

      emit(PrePostClaimsLoaded(ongoingClaims, completedClaims));
    } else {
      emitState(LoadedPrePostClaimsError(fnolHealthPolicies?.error));
    }
  }

  void onPrePostHospitalizationClaimSelection(
      BuildContext context, Claims? claim) async {
    if (claim?.claimInfo?.ahecClaimId != null) {
      claimRequest?.prePostClaimId = claim?.claimInfo?.ahecClaimId;
    } else {
      if (claim?.claimInfo?.subClaims?[0]?.patientInfo?.uhid != null)
        claimRequest?.prePostClaimantUhid =
            claim?.claimInfo?.subClaims?[0]?.patientInfo?.uhid ?? '';
      if (claim?.claimInfo?.subClaims?[0]?.subClaimId != null)
        claimRequest?.prePostSubClaimId =
            claim?.claimInfo?.subClaims?[0]?.subClaimId;
    }
    final error = await updateClaim();
    if (error == null) {
      moveToDocUploadPage();
      if (claimRequest?.selectedHealthPlanPolicy != null)
        claimRequest?.selectedHealthPlanPolicy = null;
    } else
      UiUtils.getInstance.showSnackBar(error.errorMessage ?? '', context);
  }

  void redirectToPolicyPayment(PolicyRecurringPaymentDetails policy) async {
    emitState(ShowLoader(true));
    final String? paymentLink =
        await fileClaimRepository.getPaymentLink(policy);
    emitState(ShowLoader(false));
    if (paymentLink != null) {
      emitState(RedirectToPayment(paymentLink));
    } else {
      UiUtils.getInstance.showToast("Error generating payment link");
    }
  }

  /********************************************************************
   ****************END OF PLAN SELECTION************************
   ********************************************************************/

  /********************************************************************
   ****************START OF DOCUMENT MANAGEMENT************************
   ********************************************************************/
  //converts the map for the document page to a document list
  void generateDataForDocPage(FNOLPage claimScreen) async {
    Map<DocumentHeader, List<Document>>? map;
    if (claimScreen == FNOLPage.DocUpload) {
      if (claimRequest?.documentConfig == null) await getDocumentConfig();
      map = claimRequest!.claimBills;
      if (map.isNullOrEmpty)
        map = claimRequest!.claimBills = createDocMapForPage(
            claimScreen: claimScreen, claimAmount: claimRequest!.claimAmount);
    } else if (deficiencyInfo != null) {
      map = deficiencyInfo!.queryDocuments;
    }
    if (map.isNotNullOrEmpty) updateDocList(claimScreen, map);
  }

  //get Document config(the types of documents required) from api
  Future<String?> getDocumentConfig() async {
    final result = await fileClaimRepository.getDocConfig(
        isAdvanceCashFlow || isClaimForAdvanceCash
            ? claimRequest?.acrId
            : claimRequest?.claimId);
    debugPrint('data - $result');
    if (result?.error == null) {
      resetDocsFlag =
          (claimRequest?.documentConfig?.claimDocuments?.length ?? -1) !=
              result?.claimDocuments?.length;
      if (resetDocsFlag == false) {
        int i = 0;
        while (i < (result?.claimDocuments?.length ?? 0)) {
          if (claimRequest?.documentConfig?.claimDocuments?[i].type !=
                  result?.claimDocuments?[i].type ||
              claimRequest?.documentConfig?.claimDocuments?[i].isMandatory !=
                  result?.claimDocuments?[i].isMandatory) {
            resetDocsFlag = true;
            break;
          }
          i++;
        }
      }
      claimRequest?.documentConfig = result;
      if (claimRequest?.claimBills != null) updateClaimDocsBasedOnDocConfig();
      return Future.value(null);
    } else {
      UiUtils.getInstance.showToast(result?.error?.message?.toString() ?? "");
      return Future.value(result?.error?.message?.toString());
    }
  }

  void moveToDocUploadPage() async {
    emitState(ShowLoader(true));
    String? error = await getDocumentConfig();
    emitState(ShowLoader(false));
    if (error == null) loadFNOLScreen(FNOLPage.DocUpload);
  }

  void initializeKycDocsIfRequired() {
    if (claimRequest!.kycDetails == null) {
      claimRequest!.kycDetails = KYCDetails()
        ..kycDocuments = createDocMapForPage(
            claimScreen: FNOLPage.KYCPage,
            claimAmount: claimRequest!.claimAmount);
    }
  }

//Create map for each document page and  pull out fields from existing map if any
  Map<DocumentHeader, List<Document>>? createDocMapForPage(
      {FNOLPage? claimScreen, double? claimAmount, ClaimReason? reason}) {
    Map<DocumentHeader, List<Document>> map = Map();

    IPDDocFirebaseResponse docFirebaseResponse =
        IPDDocFirebaseResponse.getData();
    if (claimScreen == FNOLPage.DocUpload) {
      List<DocumentHeader>? headers =
          claimRequest?.documentConfig?.claimDocuments;
      if (headers?.isNotNullOrEmpty ?? false) {
        for (DocumentHeader header in headers!) {
          map.addEntries([MapEntry(header, [])]);
        }
      }
      if (claimRequest?.claimBills?.entries != null &&
          (claimRequest?.claimBills?.entries.isNotEmpty ?? false)) {
        //copy existing docs to new map
        for (var existingEntry in claimRequest!.claimBills!.entries) {
          var newEntry = map.entries
              .where((element) => element.key.type == existingEntry.key.type);
          if (newEntry.isNotEmpty)
            newEntry.first.value
              ..clear()
              ..addAll(existingEntry.value);
        }
      }
    } else {
      List<DocumentHeader> headers = docFirebaseResponse.kycDocs;
      for (DocumentHeader header in headers) {
        if (header.pages?.length != null) {
          map.addAll({header: header.createEmptyDocList()});
        } else {
          map.addAll({header: []});
        }
      }
    }

    return map;
  }

  //Get map for a document page
  Map<DocumentHeader, List<Document>>? getMapForDocPage(FNOLPage claimScreen) {
    if (claimScreen == FNOLPage.DocUpload) {
      return claimRequest!.claimBills;
    } else if (claimScreen == FNOLPage.KYCPage) {
      return claimRequest!.kycDetails?.kycDocuments;
    } else if (claimScreen == FNOLPage.ClaimDeficiencyPage) {
      return deficiencyInfo!.queryDocuments;
    } else
      return {};
  }

//Toggle expanded and collapsed layout
  void toggleDocCategoryExpandedState(
      FNOLPage claimScreen, DocumentHeader documentHeader) {
    Map<DocumentHeader, List<Document>> map = getMapForDocPage(claimScreen)!;
    documentHeader.isExpanded = !documentHeader.isExpanded!;
    updateDocList(claimScreen, map);
  }

//Add a document in the doc upload list and set their state and start uploading
  void addDocument(BuildContext? context, FNOLPage claimScreen, String? type,
      List<File> files) async {
    if (claimId != null) {
      Map<DocumentHeader, List<Document>> map = getMapForDocPage(claimScreen)!;
      MapEntry<DocumentHeader, List<Document>>? entry;

      for (MapEntry<DocumentHeader, List<Document>> e in map.entries) {
        if (e.key.type == type) {
          entry = e;
          break;
        }
      }
      if (entry != null) {
        if (entry.key.numOfFiles == 0 && entry.value.isNotEmpty) {
          entry.value.removeAt(0);
        } else if (entry.key.numOfFiles > 0) {
          ///remove deleted, error files and reset the doc map with correctly uploaded files
          List<Document> docs = []..addAll(entry.value);
          for (int i = 0; i < docs.length; i++) {
            if (docs[i].localPath == null ||
                [DocStatus.ERROR, DocStatus.SELECTED]
                    .contains(docs[i].docStatus)) {
              entry.value.remove(docs[i]);
              if (entry.key.numOfFiles >= 1) entry.key.numOfFiles--;
            }
          }
        }
        for (File f in files) {
          try {
            Document d = await Document.fromFileAndHeader(entry.key, f);
            entry.value.add(d);
          } catch (e) {}
        }
        entry.key.numOfFiles = entry.value.length;
        updateDocList(claimScreen, map);
        List<Future<Document>> uploadCalls = [];
        for (Document doc in entry.value) {
          if (doc.docStatus == null || doc.docStatus == DocStatus.SELECTED) {
            uploadCalls.add(fileClaimRepository.uploadDocument(
                claimId, doc, _ClaimDocCallback(doc, this), deficiencyInfo));
          }
        }
        if (uploadCalls.isNotEmpty) {
          int count = uploadCalls.length;
          for (Future<Document> f in uploadCalls) {
            f.then((doc) {
              count--;
              if (count == 0) {
                updateDocList(claimScreen, map);
              }
            }).catchError((err) {
              count--;
              if (count == 0) {
                updateDocList(claimScreen, map);
              }
              debugPrint(err.toString());
            });
          }
        }
      }
    }
  }

//Add kyc documents, selfie and aadhar/passport/drivers licence
  void addKYCDocument(
      BuildContext? context, Document document, String? type, File file) {
    if (claimId != null) {
      AnalyticsTrackerManager.instance.sendEvent(
          event: HLTrackEvents.TAP_UPLOAD_KYC_DOCUMENT,
          properties: {"type": type});
      Map<DocumentHeader, List<Document>>? map =
          getMapForDocPage(FNOLPage.KYCPage);
      map?.forEach((key, value) async {
        if (key.type == type) {
          int i = value.indexOf(document);
          if (document.docStatus == DocStatus.UPLOADED)
            fileClaimRepository.deleteDocument(claimId, document);
          try {
            Document d = await Document.fromFileAndHeader(key, file);
            value.removeAt(i);
            value.insert(i, d);
            emitState(KYCDocStatusChanged());
            if (d.errorHandler == null) {
              await fileClaimRepository.uploadDocument(
                  claimId, d, _ClaimDocCallback(d, this), deficiencyInfo);
              emitState(KYCDocStatusChanged());
            }
          } catch (e) {}
          validateDocumentList(FNOLPage.KYCPage);
        }
      });
    }
  }

//remove a document from the list on click of delete icon
  void removeSingleDocument(FNOLPage claimScreen, Document document) async {
    document.cancelToken?.cancel();
    Map<DocumentHeader, List<Document>> map = getMapForDocPage(claimScreen)!;
    for (MapEntry<DocumentHeader, List<Document>> entry in map.entries) {
      if (entry.value.contains(document)) {
        if (document.docStatus == DocStatus.UPLOADED) {
          fileClaimRepository.deleteDocument(claimId, document);
        }
        entry.key.numOfFiles = entry.key.numOfFiles - 1;
        if (claimScreen != FNOLPage.KYCPage) {
          entry.value.remove(document);
          updateDocList(claimScreen, map);
        } else {
          int i = entry.value.indexOf(document);
          entry.value.removeAt(i);
          entry.value.insert(i, Document.emptyDocument(entry.key.type!));
          emitState(KYCDocStatusChanged());
          validateDocumentList(claimScreen);
        }
        break;
      }
    }
  }

  //remove all uploaded docs (on policy change, on draft discard etc)
  void removeAllUploadedDocs(FNOLPage screen) {
    Map<DocumentHeader, List<Document>> map = getMapForDocPage(screen) ?? {};
    for (MapEntry<DocumentHeader, List<Document>> entry in map.entries) {
      for (Document d in entry.value) {
        if (d.docStatus == DocStatus.UPLOADED) {
          fileClaimRepository.deleteDocument(claimId, d);
        }
      }
    }
    claimRequest?.claimBills?.clear();
    claimRequest?.claimBills = null;
    claimRequest?.documentConfig?.claimDocuments?.clear();
  }

//create a new list and update document list on the page
  void updateDocList(
      FNOLPage claimScreen, Map<DocumentHeader, List<Document>>? map) {
    List<DocumentBaseModel> uiItems = [];
    if (map != null) {
      for (MapEntry<DocumentHeader, List<Document>> entry in map.entries) {
        DocumentHeader key = entry.key;
        isAnyDocMandatory = isAnyDocMandatory || (key.isMandatory ?? false);
        List<Document> documents = entry.value;
        uiItems.add(key);
        if (key.numOfFiles <= 0 && entry.value.isEmpty) {
          entry.value.addAll(entry.key.createEmptyDocList());
          uiItems.addAll(entry.value);
        } else {
          if (key.isExpanded!) {
            uiItems.addAll(documents);
            if (key.numOfFiles > 0) uiItems.add(DocumentFooter(key));
          }
        }
      }
      if (deficiencyInfo?.isDeficient ?? false)
        uiItems.add(DocumentBaseModel(DocumentItemType.COMMENT));
    }
    emitState(DocsListChangedState(uiItems));
    if (map != null && map.isNotEmpty) validateDocumentList(claimScreen);
  }

  //validate all documents
  Map<String, int> _verifyDocumentsAndGetDeficiencyPage() {
    String errorMessage = "";
    int pos = 0;
    errorMessage =
        validateDocumentList(FNOLPage.DocUpload, allowEmptyDoc: false);
    if (errorMessage.isEmpty) {
      if (claimRequest?.memberSelection?.payoutMember != null &&
          claimRequest?.payoutConsent != null) {
        if (isKYCApplicable) {
          if (claimRequest?.kycDetails == null) {
            errorMessage = pan_card_error_text;
            pos = FNOLPage.PayoutMember.index;
          } else {
            String? s = claimRequest?.kycDetails?.validateKYCPage(KYCState.DOC);
            if (s != null) {
              errorMessage += s;
              pos = FNOLPage.PayoutMember.index;
            } else {
              s = claimRequest?.kycDetails?.validateKYCPage(KYCState.SELFIE);
              if (s != null) {
                errorMessage += s;
                pos = FNOLPage.KYCPage.index;
              }
            }
          }
        } else {
          //TODO: need to work on to load payout module
        }
      } else {
        pos = FNOLPage.PayoutMember.index;
      }

      if (!claimRequest!.validateBankDetails()) {
        errorMessage = "Bank account details required";
      }
    } else {
      pos = FNOLPage.DocUpload.index;
    }
    if (errorMessage.isNotEmpty)
      try {
        errorMessage = errorMessage.replaceRange(
                errorMessage.lastIndexOf(","), errorMessage.length, "") +
            " required";
      } catch (e) {}
    return {errorMessage: pos};
  }

//validating documents list for a page
  String validateDocumentList(FNOLPage claimScreen,
      {bool allowEmptyDoc = true}) {
    String error = "";
    if (claimScreen == FNOLPage.ClaimDeficiencyPage) {
      if ((deficiencyInfo?.comments?.isNullOrEmpty ?? true) &&
          (deficiencyInfo?.queryDocuments?.entries.fold(
                      0,
                      (previousValue, element) =>
                          (previousValue) ??
                          0 +
                              element.value
                                  .where((element) => element.isUploadState())
                                  .length) ??
                  0) ==
              0) {
        error = "Enter atleast one field";
      }
    }
    if (_isUploadPending(claimScreen)) {
      error = "Document uploading";
    } else {
      final docMap = getMapForDocPage(claimScreen) ?? {};
      if (!allowEmptyDoc && docMap.isEmpty) {
        error = "No documents available";
      }
      for (MapEntry<DocumentHeader, List<Document>> entry in docMap.entries) {
        if (!(entry.key.isMandatory ?? false)) continue;
        if (entry.value.isEmpty ||
            (entry.value[0].fileName == null &&
                entry.value[0].documentId == null)) {
          error += "${entry.key.title}, ";
        } else {
          bool anyOneDocIsUploaded = false;
          for (Document d in entry.value) {
            if (d.docStatus == DocStatus.UPLOADED) anyOneDocIsUploaded = true;
          }
          if (!anyOneDocIsUploaded) error += "${entry.key.title}, ";
        }
      }
    }

    Future.delayed(Duration(milliseconds: 50),
        () => emit(DocPageValidated(error.isEmpty)));

    return error;
  }

// check for doc upload state and proceed with or without alert
  bool checkUploadAndProceed(FNOLPage claimScreen, BuildContext context,
      {bool isBack = false}) {
    if (_isUploadPending(claimScreen)) {
      UiUtils.getInstance.showAlertDialog(
          context, health_ipd_title_skip_alert, txt_continue, dismiss,
          content: health_ipd_desc_skip_alert, positiveAction: () {
        _cancelUploadApiCalls(claimScreen);
        if (!isBack)
          _conditionalNavigationFromDocPage(claimScreen, context);
        else
          emitState(GoBackState());
      });
      return false;
    } else {
      if (!isBack) _conditionalNavigationFromDocPage(claimScreen, context);
      return true;
    }
  }

//to check if any document is uploading while showing alert
  bool _isUploadPending(FNOLPage claimScreen) {
    bool isUploading = false;
    final Map<DocumentHeader, List<Document>>? map =
        getMapForDocPage(claimScreen);
    if (map == null) return false;
    for (MapEntry<DocumentHeader, List<Document>> e in map.entries) {
      for (Document d in e.value) {
        if (d.docStatus == DocStatus.UPLOADING ||
            d.docStatus == DocStatus.SELECTED ||
            d.docStatus == DocStatus.PROCESSING) {
          isUploading = true;
          return isUploading;
        }
      }
    }
    return isUploading;
  }

//cancel doc upload api calls on change of page
  void _cancelUploadApiCalls(FNOLPage claimScreen) {
    Map<DocumentHeader, List<Document>> map = getMapForDocPage(claimScreen)!;
    map.entries.forEach((e) {
      List<Document> temp = List.of(e.value);
      temp.forEach((d) {
        if (d.docStatus == DocStatus.UPLOADING ||
            d.docStatus == DocStatus.SELECTED ||
            d.docStatus == DocStatus.PROCESSING) {
          d.cancelToken?.cancel("Upload cancelled");
          e.value.remove(d);
          e.key.numOfFiles = e.key.numOfFiles - 1;
        }
      });
    });
  }

  //retry document upload for failed document
  void retryDocument(FNOLPage claimScreen, Document document) {
    Map<DocumentHeader, List<Document>> map = getMapForDocPage(claimScreen)!;
    for (MapEntry<DocumentHeader, List<Document>> e in map.entries) {
      if (e.value.contains(document)) {
        document.uploadPercentage = 0;
        document.errorHandler = null;
        document.docStatus = DocStatus.UPLOADING;
        fileClaimRepository
            .uploadDocument(claimId, document,
                _ClaimDocCallback(document, this), deficiencyInfo)
            .then((value) {
          if (claimScreen == FNOLPage.KYCPage) {
            emitState(KYCDocStatusChanged());
            validateDocumentList(claimScreen);
          } else {
            updateDocList(
                claimScreen,
                getMapForDocPage(deficiencyInfo != null
                    ? FNOLPage.ClaimDeficiencyPage
                    : FNOLPage.DocUpload));
          }
        });
        emitState(DocUploadingState(document));
        // }
      }
    }
  }

//update claim docs list based on doc config
  void updateClaimDocsBasedOnDocConfig() {
    final Map<DocumentHeader, List<Document>>? claimDocs =
        getMapForDocPage(FNOLPage.DocUpload);
    if (resetDocsFlag) {
      resetDocsFlag = false;
      if (claimDocs != null) {
        List<MapEntry<DocumentHeader, List<Document>>> missingItems = [];
        for (MapEntry<DocumentHeader, List<Document>> claimDocEntry
            in claimDocs.entries) {
          // remove existing document types
          List<Document> temp = List.from(claimDocEntry.value);
          for (var document in temp) {
            if (document.docStatus == DocStatus.UPLOADED)
              fileClaimRepository.deleteDocument(claimId, document);
            claimDocEntry.value.remove(document);
            claimDocEntry.key.numOfFiles = claimDocEntry.key.numOfFiles - 1;
          }
          missingItems.add(claimDocEntry);
        }
        resetAndDeleteKYCDocs();
        missingItems.forEach((element) => claimDocs.remove(element.key));
        if (claimRequest?.documentConfig?.claimDocuments != null) {
          //adding docs which are available in docconfig but not available in claim docs
          claimRequest?.documentConfig?.claimDocuments?.forEach((docConfig) {
            if (claimDocs.keys
                .where((doc) => doc.type == docConfig.type)
                .isEmpty) {
              claimDocs.addEntries([MapEntry(docConfig, [])]);
            }
          });
        }
        claimRequest?.claimBills = claimDocs;
      }
    }
  }

  //To reset and delete kyc docs on change of member or policy
  void resetAndDeleteKYCDocs() async {
    //delete all files
    claimRequest?.kycDetails?.panCard = null;
    Map<DocumentHeader, List<Document>>? map =
        getMapForDocPage(FNOLPage.KYCPage);
    if (map != null) {
      for (MapEntry<DocumentHeader, List<Document>> entry in map.entries) {
        List<Document> temp = List.from(entry.value);
        temp.forEach((doc) async {
          if (doc.docStatus == DocStatus.UPLOADED && claimId != null) {
            fileClaimRepository.deleteDocument(claimId, doc);
          }
          int i = entry.value.indexOf(doc);
          entry.value.removeAt(i);
          entry.value.insert(i, Document.emptyDocument(entry.key.type!));
          emitState(KYCDocStatusChanged(refreshPanCardText: true));
          validateDocumentList(FNOLPage.KYCPage);
        });
      }
    }
  }

  void _submitDeficiencyDocs() async {
    emitState(LoadingFileClaimState(true));
    ErrorHandler? error = await fileClaimRepository.submitDeficiencyDocuments(
        deficiencyInfo!.claimId,
        deficiencyInfo!.deficiencyId,
        deficiencyInfo?.comments,
        acrId: acrId);
    emitState(LoadingFileClaimState(false));
    if (error == null) {
      EventManager.getInstance.emitEvent([
        HealthHomePageEventListener.REFRESH_ADVANCE_CASH_DETAILS,
        ClaimsListPageEventListener.CLAIMS_LIST_PAGE_REFRESH_EVENT,
        AdvanceCashDetailsPageEventListener.ADVANCE_CASH_DETAILS_REFRESH
      ]);
      deficiencyInfo?.queryDocuments?.clear();
      emitState(DocUploadSuccessfulState());
    } else {
      emitState(FileClaimLoadingErrorState(error));
    }
  }

  void _conditionalNavigationFromDocPage(
      FNOLPage claimScreen, BuildContext context) {
    if (claimScreen == FNOLPage.DocUpload) {
      if (isAdvanceCashFlow) {
        emitState(ShowLoader(true));
        submitClaim(context);
        emitState(ShowLoader(false));
      } else {
        loadFNOLScreen(FNOLPage.PayoutMember);
      }
    } else if (claimScreen == FNOLPage.PayoutMember) {
      if (isKYCApplicable)
        loadFNOLScreen(FNOLPage.KYCPage);
      else
        loadPayoutModule(context);
    } else if (claimScreen == FNOLPage.KYCPage) {
      loadPayoutModule(context);
    } else if (claimScreen == FNOLPage.ClaimDeficiencyPage) {
      _submitDeficiencyDocs();
    }
  }

  void updateDocumentQueryComment(String comment) {
    deficiencyInfo?.comments = comment;
    validateDocumentList(FNOLPage.ClaimDeficiencyPage);
  }

  void showDocumentPreview(Document document) {
    if ((document.localPath?.toLowerCase().endsWith("jpeg") ?? false) ||
        (document.localPath?.toLowerCase().endsWith("jpg") ?? false) ||
        (document.localPath?.toLowerCase().endsWith("png") ?? false)) {
      emitState(ShowFilePreview(document.localPath!));
    }
  }

  void validatePancardPostEditing() {
    emitState(KYCDocStatusChanged());
  }

  //For deficiency document upload flow
  void getDeficiencyDetails() async {
    if (deficiencyInfo != null) {
      if (deficiencyInfo?.claimId != null &&
          deficiencyInfo?.tpaClaimId != null &&
          deficiencyInfo?.tpaSerialNo != null) {
        deficiencyInfo =
            await fileClaimRepository.getDeficiencyDetails(deficiencyInfo!);
      } else if (deficiencyInfo?.claimId != null &&
          deficiencyInfo?.deficiencyId != null) {
        deficiencyInfo = await fileClaimRepository
            .getDeficiencyDetails(deficiencyInfo!, acrId: acrId);
      } else {
        deficiencyInfo?.queryDocuments = {
          DocumentHeader(
              type: DocumentTypes.OTHERS,
              numOfFiles: 0,
              isMandatory: true,
              title: additional_documents): []
        };
      }
      isAdditional = true;
      emitState(DeficiencyLoadedState(deficiencyInfo));
    } else {
      emitState(
          FileClaimLoadingErrorState(ErrorHandler("Something went wrong.")));
    }
  }

  /********************************************************************
   ****************END OF DOCUMENT MANAGEMENT************************
   ********************************************************************/

  /********************************************************************
   ****************START OF PAYOUT MANAGEMENT************************
   ********************************************************************/

  bool validatePayoutMemberPage() {
    bool validated = false;

    if (!isAcknowledgementNecessary && !isKYCApplicable) {
      validated = true;
    }
    if (isAcknowledgementNecessary &&
        isKYCApplicable &&
        (claimRequest?.kycDetails?.validateKYCPage(KYCState.DOC) == null &&
            claimRequest?.payoutConsent == true &&
            claimRequest?.kycDetails?.panCard != null)) validated = true;

    if (isKYCApplicable &&
        claimRequest?.kycDetails?.validateKYCPage(KYCState.DOC) == null &&
        !isAcknowledgementNecessary &&
        claimRequest?.kycDetails?.panCard != null) validated = true;

    if (isAcknowledgementNecessary &&
        (claimRequest?.payoutConsent == true) &&
        !isKYCApplicable) validated = true;

    return validated;
  }

  List<String>? getPayoutMemberList() {
    return claimRequest?.memberSelection?.policiesForSelectedPeriod
        ?.map((c) => c?.name ?? '')
        .toList();
  }

  void onPayoutMemberSelection(BuildContext context, bool consent) async {
    claimRequest?.payoutConsent = consent || !isAcknowledgementNecessary;
    final error = await updateClaim();
    if (error == null) if (isKYCApplicable) {
      loadFNOLScreen(FNOLPage.KYCPage);
    } else {
      loadPayoutModule(context);
    }
    else
      UiUtils.getInstance.showSnackBar(error.errorMessage ?? '', context);
  }

  Future<void> loadPayoutModule(BuildContext context,
      {bool refreshPage = false}) async {
    UiUtils.getInstance.buildLoading(context);
    PayoutResponse _payoutResponse = await fileClaimRepository.getPayoutRequest(
        claimRequest?.memberSelection?.payoutMember?.name ??
            claimRequest?.memberSelection?.selectedMember?.name ??
            '',
        (claimRequest?.claimId ?? 0).toString(),
        getUhidForInsured(claimRequest?.memberSelection?.payoutMember) ??
            getUhidForInsured(claimRequest?.memberSelection?.selectedMember) ??
            '');
    Navigator.pop(context);
    if (_payoutResponse.error == null) {
      PayoutResponse? receivedPayoutResponse = await AppRouter.loadPayoutPage(
          context,
          PayoutConfig(
              payoutRequestId: _payoutResponse.requestId ?? '',
              payoutLOB: PayoutLOB.health,
              supportContactNumber: contact_no,
              claimId: (claimRequest?.claimId ?? 0).toString(),
              amount: claimRequest?.claimAmount ?? 0.0,
              payoutJourney: PayoutJourney.claim,
              name: claimRequest?.memberSelection?.payoutMember?.name ?? '',
              supportEmail: health_acko_email_id,
              allowedBankAccountTypes: [PaymentMethodType.BANK],
              payoutSource: PayoutSource.health));
      if (receivedPayoutResponse != null &&
          receivedPayoutResponse.accountDetails != null) {
        claimRequest?.payoutResponse = receivedPayoutResponse;
        if (claimRequest?.bankDetails == null)
          claimRequest?.bankDetails = BankDetails();
        claimRequest?.bankDetails?.bankAccountNumber =
            receivedPayoutResponse.accountDetails!.accountNumber!;
        claimRequest?.bankDetails?.bankAccountNumberReenter =
            receivedPayoutResponse.accountDetails!.accountNumber!;
        claimRequest?.bankDetails?.ifsc =
            receivedPayoutResponse.accountDetails!.ifsc!;
        claimRequest?.bankDetails?.canSaveDetail = true;
        bool? result = claimRequest?.disclaimerCompleted;
        if (result == null || result == false) {
          var x = await Navigator.of(context).push(MaterialPageRoute(
              builder: (context) => ClaimDeclarationPage(
                  claimRequest?.memberSelection?.getProposer()?.name)));
          if (x is bool) result = x;
        }
        if (result == true) {
          claimRequest?.disclaimerCompleted = true;
          loadFNOLScreen(FNOLPage.ClaimReviewPage);
        }
        if (refreshPage) emit(RefreshReviewPage());
      }
    }
  }

  /********************************************************************
   ****************END OF PAYOUT MANAGEMENT************************
   ********************************************************************/

  /********************************************************************
   ****************START OF REVIEW PAGE************************
   ********************************************************************/
  Map<ClaimReviewSection, List<ClaimReviewDataItem>?> getReviewPageItemList() {
    return fileClaimRepository.getReviewItemList(
        claimRequest!, isKYCApplicable);
  }

  Map<String, int>? validateReviewPage() {
    String errorMessage = "";
    int pos = 0;
    if (claimRequest?.memberSelection?.selectedMember == null) {
      errorMessage = "Select the claim patient";
      pos = FNOLPage.MemberSelection.index;
    } else if (claimRequest!.date == null) {
      errorMessage = "Select the claim date";
      pos = FNOLPage.MedicalVisit.index;
    } else if (claimRequest?.memberSelection?.selectedCover == null) {
      errorMessage = "Select the claim category";
      pos = FNOLPage.FNOLCoverPage.index;
    } else if (claimRequest?.memberSelection?.payoutMember == null) {
      errorMessage = "Select member for payout";
      pos = FNOLPage.PayoutMember.index;
    } else if (claimRequest?.payoutConsent == false &&
        isAcknowledgementNecessary) {
      errorMessage = "Please provide consent for selected payout member";
      pos = FNOLPage.PayoutMember.index;
    } else {
      Map<String, int> err = _verifyDocumentsAndGetDeficiencyPage();
      errorMessage = err.keys.first;
      pos = err.values.first;
    }
    if (errorMessage.isEmpty && isKYCApplicable) {
      errorMessage = "" +
          (claimRequest?.kycDetails?.validateKYCPage(KYCState.DOC) ?? "") +
          (claimRequest?.kycDetails?.validateKYCPage(KYCState.SELFIE) ?? "");
      pos = FNOLPage.PayoutMember.index;
    }
    if (errorMessage.isNotEmpty)
      return {errorMessage: pos};
    else
      return null;
  }

  /********************************************************************
   ****************END OF REVIEW PAGE************************
   ********************************************************************/

  Map<String, dynamic> getDraftEventProps() => {
        "product": claimRequest?.selectedHealthPlanPolicy?.productType,
        "uhid": claimRequest?.selectedHealthPlanPolicy?.proposerUhid ??
            claimRequest?.selectedHealthPlanPolicy?.claimantUhid,
        "page":
            FNOLPage.values[_getInitialPageFromClaimRequest().keys.first].name
      };

  void loadNewScreenEvents(int pageNum, DSStack<int> stack) {
    try {
      isEditing = stack.top() == FNOLPage.ClaimReviewPage.index;
    } catch (ex) {
      isEditing = false;
    }
    switch (FNOLPage.values[pageNum]) {
      case FNOLPage.MemberSelection:
        triggerAmplitudeEvent(
            EventsConstants.IPD_CLAIM_MEMBERSELECTION_SCREEN,
            {
              EventKey.SOURCE: loadFromDraft == true
                  ? EventValues.claim_choice_screen
                  : EventValues.File_a_claim_cta,
              EventKey.IS_EDITING:
                  stack.contains(FNOLPage.ClaimReviewPage.index)
                      ? EventValues.true_value
                      : EventValues.false_value
            },
            feature: "health_claim");
        break;
      case FNOLPage.MedicalVisit:
        triggerAmplitudeEvent(
            EventsConstants.IPD_claim_claimamount_screen,
            {
              EventKey.IS_EDITING:
                  stack.contains(FNOLPage.ClaimReviewPage.index)
                      ? EventValues.true_value
                      : EventValues.false_value
            },
            feature: "health_claim");
        break;
      case FNOLPage.DocUpload:
        triggerAmplitudeEvent(
            EventsConstants.IPD_claim_documentupload_screen1,
            {
              EventKey.IS_EDITING:
                  stack.contains(FNOLPage.ClaimReviewPage.index)
                      ? EventValues.true_value
                      : EventValues.false_value
            },
            feature: "health_claim");
        break;
      case FNOLPage.KYCPage:
        triggerAmplitudeEvent(
            EventsConstants.IPD_claim_kyc_screen,
            {
              EventKey.IS_EDITING:
                  stack.contains(FNOLPage.ClaimReviewPage.index)
                      ? EventValues.true_value
                      : EventValues.false_value
            },
            feature: "health_claim");
        break;
      case FNOLPage.PayoutMember:
        triggerAmplitudeEvent(
            EventsConstants.IPD_claim_bank_details_screen,
            {
              EventKey.IS_EDITING:
                  stack.contains(FNOLPage.ClaimReviewPage.index)
                      ? EventValues.true_value
                      : EventValues.false_value
            },
            feature: "health_claim");
        break;
      default:
        break;
    }
  }

  Future<void> saveDraft() async {
    try {
      if (claimRequest?.date != null &&
          !isAdvanceCashFlow &&
          !isClaimForAdvanceCash) {
        var map = await getDraftedClaimFromPrefs();
        if (map == null)
          map = {"claim": claimRequest!.toMap()};
        else
          map.addAll({"claim": claimRequest!.toMap()});
        await setStringPrefs(
            StringDataSharedPreferenceKeys.IPD_CLAIM_DRAFT, jsonEncode(map));
      }
    } catch (ex) {
      debugPrint(ex.toString());
    }
  }

  Future<void> discardDraft() async {
    saveState = false;
    await setStringPrefs(StringDataSharedPreferenceKeys.IPD_CLAIM_DRAFT, null);
  }

  Future<ClaimRequest?> _getDraftState() async {
    Map<String, dynamic>? draft = await getDraftedClaimFromPrefs();
    ClaimRequest? claimRequest;
    if (draft != null) {
      try {
        claimRequest = ClaimRequest.fromMap(draft);
      } catch (ex, stack) {
        debugPrint(ex.toString());
        debugPrintStack(stackTrace: stack);
      }
    }
    return Future.value(claimRequest);
  }

  Future<ErrorHandler?> updateClaim({bool isFromDuplicateModal = false}) async {
    //used when no need to show loader & screen handling
    if (isAdvanceCashFlow) {
      try {
        claimRequest = await fileClaimRepository.createUpdateACR(claimRequest);
      } catch (ex) {
        return Future.value(
          ErrorHandler(
            ex.toString(),
          ),
        );
      }
    } else {
      if (claimRequest!.claimAmount != null &&
          claimRequest!.claimAmount! > 0 &&
          claimRequest!.date != null &&
          claimRequest!.claimAmount! <=
              claimRequest!.memberSelection!.maxSumInsured &&
          claimRequest?.memberSelection?.selectedMember != null &&
          claimRequest?.claimId != null) {
        if (!isFromDuplicateModal) emitState(ShowLoader(true));
        claimRequest?.error = null;
        claimRequest = await fileClaimRepository.updateClaim(claimRequest);
        if (!isFromDuplicateModal) emitState(ShowLoader(false));
        if (claimRequest?.error != null)
          return Future.value(claimRequest?.error);
      }
    }
    return Future.value(null);
  }

  void submitClaim(BuildContext context) async {
    if (_submitClaimClickEnabled == false) {
      UiUtils.getInstance.showToast("In Progress");
      return;
    }
    _submitClaimClickEnabled = false;
    triggerAmplitudeEvent(EventsConstants.ipd_submit_claim_click, {},
        feature: "health_claim");
    claimRequest = await fileClaimRepository.submitClaim(claimRequest);
    _submitClaimClickEnabled = true;

    if (claimRequest != null && claimRequest?.error == null) {
      EventManager.getInstance.emitEvent([
        HealthHomePageEventListener.REFRESH_ADVANCE_CASH_DETAILS,
        ClaimsListPageEventListener.CLAIMS_LIST_PAGE_REFRESH_EVENT,
        AdvanceCashDetailsPageEventListener.ADVANCE_CASH_DETAILS_REFRESH
      ]);
      await discardDraft();
      if (isAdvanceCashFlow) {
        Navigator.pushReplacement(context, MaterialPageRoute(
            builder: (_) => AdvanceCashStatusPage(
                AdvanceCashStatusScreens.RequestSubmitted,
                acrId: claimRequest?.acrId)));
      } else {
        Navigator.pushReplacement(context, MaterialPageRoute(
            builder: (_) => BlocProvider<ClaimSuccessCubit>(
                create: (context) => ClaimSuccessCubit(
                    ahecClaimId: claimId.toString(),
                    allSubClaimsInfo: <ClaimInfoSummary?>[]),
                child: ClaimSuccessPage())));
      }
    } else {
      UiUtils.getInstance.showToast(claimRequest!.error!.message!);
    }
  }
}

class _ClaimDocCallback implements FileProgressCallback {
  Document _document;

  FileClaimBloc _fileClaimBloc;

  _ClaimDocCallback(this._document, this._fileClaimBloc);

  @override
  void percentProgress(double percentage) {
    if (percentage < 1) {
      _document.docStatus = DocStatus.UPLOADING;
    } else {
      _document.docStatus = DocStatus.PROCESSING;
    }
    _document.uploadPercentage = percentage;
    _fileClaimBloc.emitState(DocUploadingState(_document));
  }

  @override
  void onError(dynamic err, {ErrorInterceptorHandler? handler}) {
    _document.docStatus = DocStatus.ERROR;
    _fileClaimBloc.emitState(DocUploadingState(_document));
  }
}

extension SegmentEventTracking on FileClaimBloc {
  void triggerScreenEvent(HLPageEvents event, Map<String, dynamic>? params) {
    AnalyticsTrackerManager.instance
        .sendEvent(event: event, properties: _getEventParams(params));
  }

  void triggerTrackEvent(HLTrackEvents event, Map<String, dynamic>? params) {
    AnalyticsTrackerManager.instance
        .sendEvent(event: event, properties: _getEventParams(params));
  }

  Map<String, dynamic> _getEventParams(Map<String, dynamic>? params) {
    Map<String, dynamic> finalParams = {
      "journey": "FNOL",
      "platform": Platform.isAndroid ? "Android" : "iOS",
    };
    if (claimRequest?.claimId != null) {
      finalParams["uhid"] = claimRequest?.claimId!;
    }
    if (params != null && params.length > 0) {
      finalParams.addAll(params);
    }
    return finalParams;
  }
}
