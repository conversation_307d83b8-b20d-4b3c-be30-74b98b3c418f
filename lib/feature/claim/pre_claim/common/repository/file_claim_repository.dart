import 'dart:async';
import 'dart:io';

import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/claim/advance_cash/common/advance_cash_repository.dart';
import 'package:acko_flutter/feature/claim/post_claim/common/models/claim_status_model.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/models/claim_entities.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/models/doc_upload_entity.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/models/member_selection_entity.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/models/treatment_entity.dart';
import 'package:acko_flutter/feature/health_commons/health_network_layer.dart';
import 'package:acko_flutter/feature/health_home/models/health_header_data_model.dart';
import 'package:acko_flutter/feature/health_home/models/health_header_policy_details_model.dart';
import 'package:acko_flutter/feature/health_policy/repository/health_policy_repository.dart';
import 'package:acko_flutter/feature/recurring_payment/recurring_payment_models.dart';
import 'package:acko_flutter/feature/recurring_payment/recurring_payment_repository.dart';
import 'package:acko_flutter/network/ApiResponse.dart';
import 'package:acko_flutter/r2d2/model/data.dart';
import 'package:policy_details/policy_details.dart';
import 'package:utilities/constants/constants.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:acko_flutter/util/health/utils.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:dio/dio.dart' as dio;
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:http_parser/src/media_type.dart' show MediaType;
import 'package:intl/intl.dart';
import 'package:networking_module/networking_module.dart';
import 'package:networking_module/util/file_progress_callback.dart';
import 'package:sprintf/sprintf.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/enums.dart';
import '../../../../payment/payout/models/payout_models.dart';
import '../../../my_claims_home/model/my_claims_models.dart';
import '../models/fnol_cover_page_entity.dart';
import '../models/health_policies.dart';

class FileClaimRepository {
  final HealthPolicyRepository _healthRepo = new HealthPolicyRepository();
  final HealthAssetDetailsRepo _healthAssetRepo = HealthAssetDetailsRepo();
  final bool isAdvanceCashFlow;
  bool isClaimForAdvanceCash;
  final AdvanceCashRepository _advanceCashDetailsRepository =
      AdvanceCashRepository();
  final RecurringPaymentRepository _recurringPaymentRepository =
      RecurringPaymentRepository();

  FileClaimRepository(
      {this.isAdvanceCashFlow = false, this.isClaimForAdvanceCash = false});

  Future<ClaimRequest> updateClaim(ClaimRequest? claimRequest) async {
    if (!await Util.isNetworkConnected())
      return Future.value(
          ClaimRequest.error(ErrorHandler(NetworkError(ErrorType.noInternet))));
    Map<String, dynamic> map =
        claimRequest?.getRequestMap(claimRequest.claimId != null) ?? {};
    map.removeWhere((key, value) => value == null);
    ApiResponse apiResponse;
    if (claimRequest?.claimId != null) {
      if (claimRequest?.kycDetails != null &&
          claimRequest?.kycDetails?.panCard != null &&
          RegExp(HealthConstants.PAN_REGEX)
              .hasMatch(claimRequest!.kycDetails!.panCard!))
        map['pan_number'] = claimRequest.kycDetails?.panCard;
      final response = await HealthNetworkLayer.instance
          .makeApiRequest<AckoResponse>(ApiRequest(
              apiPath: "${ApiPath.createClaim}/${claimRequest?.claimId}",
              requestType: RequestType.patch,
              requestBody: map));
      response.response.fold(
          (l) => claimRequest?.error = ErrorHandler(l.error),
          (r) => claimRequest?.claimId = r.data['id'] == null
              ? claimRequest.claimId
              : r.data['id'] as int);
    }
    return Future.value(claimRequest);
  }

  Future<ClaimRequest> submitClaim(ClaimRequest? claimRequest) async {
    if (!await Util.isNetworkConnected())
      return Future.value(
          ClaimRequest.error(ErrorHandler(NetworkError(ErrorType.noInternet))));
    final Response<AckoResponse> response;
    if (isAdvanceCashFlow) {
      response = await _advanceCashDetailsRepository.submitClaim(
          claimRequest!.claimId, claimRequest.acrId);
    } else {
      response = await HealthNetworkLayer.instance.makeApiRequest(ApiRequest(
          apiPath: ApiPath.submitClaim,
          requestType: RequestType.post,
          requestBody: {"claimId": claimRequest!.claimId}));
    }
    response.response
        .fold((l) => claimRequest.error = ErrorHandler(l.error), (r) => null);
    return Future.value(claimRequest);
  }

  Future<Document> uploadDocument(int? claimId, Document document,
      FileProgressCallback callback, DeficiencyInfo? deficiencyInfo) async {
    var requestData;
    var mediaType = MediaType(
        document.mimeType?.split("/").first ?? "application",
        document.mimeType?.split("/").last ?? "*");
    if (deficiencyInfo?.deficiencyId == null) {
      requestData = {
        "type": document.type,
        "claimId": claimId,
        "file": await dio.MultipartFile.fromFile(document.localPath!,
            filename: document.fileName, contentType: mediaType)
      };
    } else {
      requestData = {
        "type": document.type,
        "claimId": claimId,
        "file": await dio.MultipartFile.fromFile(document.localPath!,
            filename: document.fileName, contentType: mediaType),
        "tpaClaimId": deficiencyInfo?.tpaClaimId,
        "tpaSerialNo": deficiencyInfo?.tpaSerialNo,
        "deficiencyId": deficiencyInfo?.deficiencyId
      };
    }
    document.cancelToken = dio.CancelToken();
    final response = await HealthNetworkLayer.instance.makeApiRequest<dynamic>(
        ApiRequest(
            apiPath: ApiPath.claimDocument,
            requestType: RequestType.postFormData,
            formData: requestData,
            cancelToken: document.cancelToken,
            fileRequestProgressCallback: callback));

    response.response.fold((l) {
      document.docStatus = DocStatus.ERROR;
      document.errorHandler = ErrorHandler(l.failureMessage);
      document.errorHandler!.image = null;
    }, (r) {
      document.docStatus = DocStatus.UPLOADED;
      document.documentId = r.data['document_id'].toString();
      document.uploadedUrl = r.data['file_url'];
    });
    AnalyticsTrackerManager.instance
        .sendEvent(event: HLTrackEvents.DOCUMENT_UPLOAD_STATUS, properties: {
      "status": "${document.docStatus}",
      "mime_type": "${document.mimeType}",
      "failure_reason": "${document.errorHandler?.message}",
      "title": "${document.fileName}"
    });
    return Future.value(document);
  }

  /*Future<Document> uploadDocument(int? claimId, Document document,
      FileProgressCallback callback, DeficiencyInfo? deficiencyInfo) async {
    var requestData;
    var mediaType = MediaType(
        document.mimeType?.split("/").first ?? "application",
        document.mimeType?.split("/").last ?? "*");
    if (deficiencyInfo?.deficiencyId == null) {
      requestData = {
        "type": document.type,
        "claimId": claimId,
        "file": await dio.MultipartFile.fromFile(document.localPath!,
            filename: document.fileName, contentType: mediaType)
      };
    } else {
      requestData = {
        "type": document.type,
        "claimId": claimId,
        "file": await dio.MultipartFile.fromFile(document.localPath!,
            filename: document.fileName, contentType: mediaType),
        "tpaClaimId": deficiencyInfo?.tpaClaimId,
        "tpaSerialNo": deficiencyInfo?.tpaSerialNo,
        "deficiencyId": deficiencyInfo?.deficiencyId
      };
    }
    document.cancelToken = dio.CancelToken();
    ApiResponse apiResponse = await ApiService.apiServiceInstance.postFormData(
        ApiPath.uploadIpdDocument,
        formData: requestData,
        cancelToken: document.cancelToken,
        fileUploadProgressCallback: callback);
    if (apiResponse.error != null) {
      document.docStatus = DocStatus.ERROR;
      document.errorHandler = apiResponse.error;
      document.errorHandler!.image = null;
    } else {
      document.docStatus = DocStatus.UPLOADED;
      document.documentId = apiResponse.data['document_id'].toString();
      document.uploadedUrl = apiResponse.data['file_url'];
    }

    TrackerManager.instance.instrumentUserAction(
        SegmentEvents.DOCUMENT_UPLOAD_STATUS,
        {
          "status": "${document.docStatus}",
          "mime_type": "${document.mimeType}",
          "failure_reason": "${document.errorHandler?.message}",
          "title": "${document.fileName}"
        },
        specificPlatform: AnalyticsPlatform.SEGMENT_EVENT);
    return Future.value(document);
  }*/

  Future<dynamic> deleteDocument(int? claimId, Document document) async {
    try {
      Response<AckoResponse<dynamic>>? response;
      if (document.documentId?.isNotEmpty ?? false) {
        AnalyticsTrackerManager.instance.sendEvent(
            event: HLTrackEvents.TAP_DELETE_DOCUMENT,
            properties: {
              "mime_type": "${document.mimeType}",
              "title": "${document.fileName}"
            });
        response = await HealthNetworkLayer.instance
            .makeApiRequest<AckoResponse>(ApiRequest(
                apiPath: ApiPath.claimDocument,
                requestType: RequestType.delete,
                requestBody: {
              'uploadId': int.tryParse(document.documentId ?? '')
            }));
      }
      if (document.localPath != null) File(document.localPath!).delete();
      if (response != null) {
        response.response.fold((l) {
          document.docStatus = DocStatus.ERROR;
          document.errorHandler = ErrorHandler(l.failureMessage);
        }, (r) {
          document.docStatus = DocStatus.DELETED;
        });
      } else {
        document.docStatus = DocStatus.DELETED;
      }
    } catch (error, stackTrace) {
      FirebaseCrashlytics.instance.recordError(error, stackTrace);
      debugPrint("${error.toString()}");
    }
    return Future.value(document);
  }

  Future<DeficiencyInfo> getDeficiencyDetails(DeficiencyInfo deficiencyInfo,
      {int? acrId}) async {
    if (!await Util.isNetworkConnected())
      return Future.value(DeficiencyInfo()
        ..error = ErrorHandler(NetworkError(ErrorType.noInternet)));
    final Response<AckoResponse> response;
    if (isAdvanceCashFlow) {
      response = await _advanceCashDetailsRepository.getDocConfig(acrId);
      response.response.fold(
          (l) => deficiencyInfo = DeficiencyInfo()
            ..error = ErrorHandler(l.failureMessage), (r) {
        deficiencyInfo.queryDocuments = {}..addEntries(
            DocumentConfig.fromJson(r.data)
                .claimDocuments!
                .map((e) => MapEntry(e, <Document>[]))
                .toList());
      });
    } else {
      response = await HealthNetworkLayer.instance.makeApiRequest<AckoResponse>(
          ApiRequest(
              apiPath: ApiPath.deficiencyDetails,
              requestType: RequestType.get,
              queryParameters: {
            "claim-id": deficiencyInfo.claimId,
            "tpa-claim-id": deficiencyInfo.tpaClaimId,
            "sl-no": deficiencyInfo.tpaSerialNo
          }));

      response.response.fold(
          (l) => deficiencyInfo = DeficiencyInfo()
            ..error = ErrorHandler(l.failureMessage),
          (r) => deficiencyInfo = DeficiencyInfo.fromMap(r.data));
    }
    return Future.value(deficiencyInfo);
  }

  Future<ErrorHandler?> submitDeficiencyDocuments(
      int? claimId, int? deficiencyId, String? comments,
      {int? acrId}) async {
    if (!await Util.isNetworkConnected())
      return Future.value(ErrorHandler(NetworkError(ErrorType.noInternet)));
    ApiResponse response;
    if (isAdvanceCashFlow) {
      return _advanceCashDetailsRepository.resolveDeficiency(
          claimId, acrId, deficiencyId, comments);
    } else {
      String apiEndpoint = deficiencyId == null
          ? sprintf(ApiPath.submitAdditionalDocs, [claimId])
          : sprintf(ApiPath.resolveDeficiency, [claimId, deficiencyId]);
      final response = await HealthNetworkLayer.instance
          .makeApiRequest<AckoResponse>(ApiRequest(
              apiPath: apiEndpoint,
              requestType: RequestType.post,
              requestBody: {"comments": comments}));
      ErrorHandler? errorHandler = null;
      response.response
          .fold((l) => ErrorHandler(l.failureMessage), (r) => null);
      return Future.value(errorHandler);
    }
  }

  Future<ErrorHandler?> updateCommunicationEmail(
      String? communicationEmail, String? claimId) async {
    if (!await Util.isNetworkConnected())
      return Future.value(ErrorHandler(NetworkError(ErrorType.noInternet)));
    final response = await HealthNetworkLayer.instance
        .makeApiRequest<AckoResponse>(ApiRequest(
            apiPath: "${ApiPath.createClaim}/$claimId",
            requestType: RequestType.patch,
            requestBody: {"claim_email": communicationEmail}));
    ErrorHandler? errorHandler = null;
    response.response.fold((l) => ErrorHandler(l.failureMessage), (r) => null);
    return Future.value(errorHandler);
  }

  Future<ErrorHandler?> addPanNumber(ClaimRequest? claimRequest) async {
    if (!await Util.isNetworkConnected())
      return Future.value(ErrorHandler(NetworkError(ErrorType.noInternet)));
    Map<String, dynamic> map = {
      "pan_number": claimRequest?.kycDetails?.panCard
    };
    final response = await HealthNetworkLayer.instance
        .makeApiRequest<AckoResponse>(ApiRequest(
            apiPath: "${ApiPath.createClaim}/${claimRequest?.claimId}",
            requestType: RequestType.patch,
            requestBody: map));
    ErrorHandler? errorHandler = null;
    response.response.fold((l) => ErrorHandler(l.failureMessage), (r) => null);
    return Future.value(errorHandler);
  }

  List<String> getUhidForInsured(
      HealthAssetDetailsResponseInsured? selectedUser) {
    List<String> uhids = [];
    selectedUser?.policies?.forEach((policy) {
      policy.insureds?.forEach((insured) {
        if (selectedUser.name.equalsIgnoreCase(insured.name) &&
            insured.uhid.isNotNullOrEmpty &&
            !uhids.contains(insured.uhid)) {
          uhids.add(insured.uhid!);
        }
      });
    });
    return uhids;
  }

  Future<FNOLCoverPageResponse?> getFNOLCoverPageData(
      ClaimRequest claimRequest) async {
    FNOLCoverPageResponse? coverPageResponse;
    if (!await Util.isNetworkConnected()) {
      coverPageResponse = FNOLCoverPageResponse.error(
          ErrorHandler(NetworkError(ErrorType.noInternet)));
      return Future.value();
    }
    final uhids =
        getUhidForInsured(claimRequest.memberSelection?.selectedMember);
    if (claimRequest.date != null &&
        claimRequest.claimAmount != null &&
        uhids.isNotNullOrEmpty) {
      try {
        final response = await HealthNetworkLayer.instance
            .makeApiRequest<AckoResponse>(ApiRequest(
                apiPath: ApiPath.coverPage,
                requestType: RequestType.put,
                requestBody: {
              "admission_date":
                  DateFormat('yyyy-MM-dd').format(claimRequest.date!),
              "claim_amount": claimRequest.claimAmount,
              if (claimRequest.claimId != null)
                "fnol_request_id": claimRequest.claimId,
              "selected_member_uhids": uhids,
              "incident_reason": claimRequest.incidentReason,
              "action": claimRequest.claimId == null ? "INSERT" : "UPDATE"
            }));
        response.response.fold(
            (l) => coverPageResponse =
                FNOLCoverPageResponse(error: ErrorHandler(l.failureMessage)),
            (r) {
          coverPageResponse = FNOLCoverPageResponse.fromJson(r.data);
          claimRequest.claimId = coverPageResponse?.claimId;
        });
      } catch (ex) {
        coverPageResponse = FNOLCoverPageResponse(
            error: ErrorHandler(dio.DioExceptionType.cancel));
      }
    }
    return Future.value(coverPageResponse);
  }

  Future<PayoutResponse> getPayoutRequest(
      String payeeName, String claimID, String payeeUHID) async {
    Map<String, dynamic> params = {
      'payee_name': payeeName,
      'payee_uhid': payeeUHID
    };
    final response = await HealthNetworkLayer.instance
        .makeApiRequest<AckoResponse>(ApiRequest(
            apiPath: sprintf(ApiPath.payout_request, [claimID]),
            requestType: RequestType.post,
            requestBody: params));

    PayoutResponse? payoutResponse = null;
    response.response.fold(
        (l) => payoutResponse = PayoutResponse()
          ..error = ErrorHandler(l.failureMessage),
        (r) => payoutResponse = PayoutResponse.fromJson(r.data));
    return Future.value(payoutResponse);
  }

  Future<MemberSelection> getMemberSelection() async {
    HealthHeaderDataModel response =
        await HealthHeaderApiRepo.instance.getHealthHeaderData();
    if (response.error == null) {
      List<HealthHeaderPolicyDetailsModel> policies = []
        ..addAll(response.retailPolicies)
        ..addAll(response.gmcPolicies);
      final dates = response.getExtremeDatesForAllPolicies();
      DateTime extremeStartDate = DateTime(2018, 1, 1, 0, 0, 0, 0, 0).toLocal();
      DateTime extremeEndDate = dates[1].toLocal();
      if (isAdvanceCashFlow) {
        extremeStartDate = DateTime.now()
            .add(Duration(days: DateTime.now().hour < 18 ? 1 : 2));
        extremeEndDate =
            extremeEndDate.getMinDate(DateTime.now().add(Duration(days: 60)));
      }
      double maxSumInsured = 0;
      policies.forEach((element) {
        if (isAdvanceCashFlow) {
          if (element.productType == HealthConstants.ENTERPRISE_PRODUCT) {
            maxSumInsured += element.sumInsured?.value ?? 0;
          }
        } else
          maxSumInsured += element.sumInsured?.value ?? 0;
      });
      return Future.value(
          MemberSelection(extremeStartDate, extremeEndDate, maxSumInsured));
    }
    return Future.error("couldn't find start date and end dates");
  }

  Future<List<HealthAssetDetailsResponseInsured?>?> getPoliciesForADate(
      DateTime date) async {
    HealthAssetDetailsResponse? assetResponse =
        await _healthAssetRepo.getAssetDetails(
            apiType: ProjectionApiType.projectionLite,
            allPolicies: true,
            filterRenewed: false,
            admissionDate: date,
            filterAsp: false);
    if (assetResponse.error == null) {
      return Future.value(assetResponse.insureds);
    } else {
      return Future.error(assetResponse.error!);
    }
  }

  Future<TreatmentResponse> searchTreatment(
      String value, dio.CancelToken? cancelToken) async {
    Map<String, dynamic> query = {};
    if (value.isNotEmpty) {
      /**/
      query["treatment_name"] = value;
    } else {
      query["priority"] = 1;
    }
    final response = await HealthNetworkLayer.instance
        .makeApiRequest<AckoResponse>(ApiRequest(
            apiPath: ApiPath.treatmentSearch,
            requestType: RequestType.get,
            queryParameters: query,
            cancelToken: cancelToken));
    TreatmentResponse? treatmentResponse = null;
    response.response.fold(
        (l) => treatmentResponse = TreatmentResponse([])
          ..error = ErrorHandler(l.failureMessage),
        (r) => treatmentResponse = TreatmentResponse.fromJson(r.data));
    return Future.value(treatmentResponse);
  }

  Future<ClaimHealthPolicies?> getHealthPoliciesFromServer(
      ClaimRequest claimRequest) async {
    ClaimHealthPolicies? healthPoliciesResponse = null;
    if (!await Util.isNetworkConnected()) {
      healthPoliciesResponse = ClaimHealthPolicies.error(
          ErrorHandler(NetworkError(ErrorType.noInternet)));
      return Future.value();
    }
    final response = await HealthNetworkLayer.instance
        .makeApiRequest<AckoResponse>(ApiRequest(
            apiPath: ApiPath.policyInfo,
            requestType: RequestType.put,
            requestBody: {
          "fnol_request_id": claimRequest.claimId,
          "cover_id": claimRequest.treatment?.coverId,
          "treatment_id": claimRequest.treatment?.code,
          if (claimRequest.treatment?.treatmentPath != null)
            "treatment_path": claimRequest.treatment?.treatmentPath
        }));

    response.response.fold(
        (l) => healthPoliciesResponse = ClaimHealthPolicies()
          ..error = ErrorHandler(l.failureMessage),
        (r) => healthPoliciesResponse = ClaimHealthPolicies.fromJson(r.data));
    return Future.value(healthPoliciesResponse);
  }

  Future<DocumentConfig?> getDocConfig(int? claimId) async {
    DocumentConfig? documentConfig;
    if (!await Util.isNetworkConnected())
      return Future.value(DocumentConfig.error(
          ErrorHandler(NetworkError(ErrorType.noInternet))));
    final Response<AckoResponse> response;
    if (isAdvanceCashFlow || isClaimForAdvanceCash) {
      response = await _advanceCashDetailsRepository.getDocConfig(claimId);
    } else {
      response = await HealthNetworkLayer.instance.makeApiRequest<AckoResponse>(
          ApiRequest(
              apiPath: ApiPath.docConfig,
              requestType: RequestType.get,
              queryParameters: {"fnol-request-id": claimId}));
    }
    response.response.fold(
        (l) => documentConfig =
            DocumentConfig(error: ErrorHandler(l.failureMessage)),
        (r) => documentConfig = DocumentConfig.fromJson(r.data));
    return Future.value(documentConfig);
  }

  Future<ClaimCards?> getClaimCards(
      List<String?>? ownerUhids, int pageNumber) async {
    ClaimCards? claimCards;
    if (!await Util.isNetworkConnected())
      return Future.value(
          ClaimCards.error(ErrorHandler(NetworkError(ErrorType.noInternet))));
    final response = await HealthNetworkLayer.instance
        .makeApiRequest<AckoResponse>(ApiRequest(
            apiPath: ApiPath.healthClaimCardsApi,
            requestType: RequestType.get,
            queryParameters: {
          "uhid": ownerUhids,
          "cover_type": "ipd",
          "page": "$pageNumber"
        }));
    response.response.fold(
        (l) => claimCards = ClaimCards(error: ErrorHandler(l.failureMessage)),
        (r) => claimCards = ClaimCards.fromJson(r.data));

    return Future.value(claimCards);
  }

  Future<ClaimRequest> createUpdateACR(ClaimRequest? claimRequest) async {
    try {
      final Map? map = claimRequest?.getACRMap();
      if (map != null) {
        final response = await _advanceCashDetailsRepository.createUpdateACR(
            claimRequest?.claimId, claimRequest?.acrId, map);
        response.response.fold(
            (l) => claimRequest?.error = ErrorHandler(l.failureMessage), (r) {
          claimRequest?.acrId = r.data['acr_id'] ?? claimRequest.acrId;
          claimRequest?.claimId = r.data['fnol_req_id'] ?? claimRequest.claimId;
        });
      }
    } catch (ex, stack) {
      debugPrintStack(stackTrace: stack);
    }
    return Future.value(claimRequest);
  }

  Future<ClaimRequest?> getClaimRequestFromAdvCash(
      int acrId, ClaimRequest? claimRequest) async {
    final acrResponse =
        await _advanceCashDetailsRepository.getAdvanceCashDetails(acrId);
    if (claimRequest == null) claimRequest = ClaimRequest();
    if (acrResponse.error == null) {
      try {
        DateTime admissionDate =
            DateFormat('dd-MMM-yyyy').parse(acrResponse.admissionDate ?? "");
        final policyResponse = await getPoliciesForADate(admissionDate);
        claimRequest.memberSelection?.policiesForDate(policyResponse, true);
        claimRequest.claimAmount = double.parse(acrResponse.claimAmount!);
        claimRequest.claimId = acrResponse.claimId;
        claimRequest.date = admissionDate;
        claimRequest.memberSelection?.selectedCover = CategoryCovers.fromJson({
          "category": "Hospitalisation",
          "category_type": "IPD",
          "icon_url":
              "https://health-dev.ackoassets.com/gmc/benefits-new/pre_post_hospitalisation.svg",
          "cover_key": "multi_day_hospitalisation",
          "cover_name": "Multi-day hospitalisation",
          "claim_type_display_string": "Hospitalisation"
        });
        claimRequest.acrId = acrId;
        final proposer = claimRequest.memberSelection
            ?.getPolicyByUhid(acrResponse.advanceCashInfo!.proposerUhid!);
        final claimant = claimRequest.memberSelection
            ?.getPolicyByUhid(acrResponse.patientInfo!.uhid!);
        claimRequest.memberSelection?.selectedMember = claimant;
        claimRequest.memberSelection?.proposer = proposer;
        claimRequest.selectedHealthPlanPolicy = null;
        claimRequest.prePostClaimId = null;
        claimRequest.prePostClaimantUhid = null;
        claimRequest.prePostSubClaimId = null;
        return Future.value(claimRequest);
      } catch (e, stack) {
        debugPrintStack(stackTrace: stack);
        return Future.value(null);
      }
    } else {
      return Future.value(null);
    }
  }

  Map<ClaimReviewSection, List<ClaimReviewDataItem>?> getReviewItemList(
      ClaimRequest claimRequest, bool isKYCApplicable) {
    Map<ClaimReviewSection, List<ClaimReviewDataItem>?> reviewSections = {};
    reviewSections[ClaimReviewSection.treatmentInfo] = [
      ClaimReviewDataItem(
          admission_date,
          false,
          FNOLPage.MedicalVisit.index.toString(),
          claimRequest.date?.formatDate() ?? "",
          EventValues.cta_edit_admission_date),
      ClaimReviewDataItem(
          claim_amount,
          false,
          FNOLPage.MedicalVisit.index.toString(),
          "$rupeeSymbol ${NumberUtils.commaSeparatedNumber(claimRequest.claimAmount != null ? claimRequest.claimAmount!.toDouble() : 0.0)}",
          EventValues.cta_edit_claim_amount),
      ClaimReviewDataItem(
          patient,
          false,
          FNOLPage.MemberSelection.index.toString(),
          claimRequest.memberSelection?.selectedMember?.name,
          EventValues.cta_edit_name),
      ClaimReviewDataItem(
          selected_cover,
          false,
          FNOLPage.FNOLCoverPage.index.toString(),
          claimRequest.memberSelection?.selectedCover?.category ??
              "Multi-day hospitalization",
          EventValues.cta_edit_cover)
    ];
    reviewSections[ClaimReviewSection.documents] = List.empty(growable: true);
    if (claimRequest.claimBills != null) {
      for (MapEntry<DocumentHeader, List<Document>> e
          in claimRequest.claimBills!.entries) {
        if (e.key.numOfFiles > 0) {
          int total = 0;
          e.value.forEach((element) {
            if (element.errorHandler == null) total++;
          });
          if (total != 0) {
            reviewSections[ClaimReviewSection.documents] =
                reviewSections[ClaimReviewSection.documents]
                  ?..add(ClaimReviewDataItem(
                      e.key.title,
                      true,
                      FNOLPage.DocUpload.index.toString(),
                      total.toString() + (total == 1 ? " file" : " files"),
                      'cta_edit_${e.key.type}'));
          }
        }
      }
    }
    if (claimRequest.kycDetails?.kycDocuments != null && isKYCApplicable) {
      if (claimRequest.kycDetails?.panCard != null) {
        reviewSections[ClaimReviewSection.documents] =
            reviewSections[ClaimReviewSection.documents]
              ?..add(ClaimReviewDataItem(
                  pan_card_number,
                  false,
                  FNOLPage.PayoutMember.index.toString(),
                  claimRequest.kycDetails?.panCard,
                  'cta_edit_pancard'));
      }
      for (MapEntry<DocumentHeader, List<Document>> e
          in claimRequest.kycDetails!.kycDocuments!.entries) {
        int total = 0;
        e.value.forEach((element) {
          if (element.docStatus == DocStatus.UPLOADED) total++;
        });
        if (total != 0) {
          reviewSections[ClaimReviewSection.documents] =
              reviewSections[ClaimReviewSection.documents]
                ?..add(ClaimReviewDataItem(
                    e.key.title,
                    true,
                    e.key.type == DocumentTypes.SELFIE
                        ? FNOLPage.KYCPage.index.toString()
                        : FNOLPage.PayoutMember.index.toString(),
                    total.toString() + (total == 1 ? " file" : " files"),
                    'cta_edit_${e.key.type}'));
        }
      }
    }

    reviewSections[ClaimReviewSection.accountDetails] =
        List.empty(growable: true);
    if (claimRequest.memberSelection?.payoutMember != null) {
      reviewSections[ClaimReviewSection.accountDetails] =
          reviewSections[ClaimReviewSection.accountDetails]
            ?..add(ClaimReviewDataItem(
                pay_amount,
                false,
                FNOLPage.PayoutMember.index.toString(),
                claimRequest.memberSelection?.payoutMember?.name ?? '',
                EventValues.cta_edit_payout_member));
    }

    if (claimRequest.validateBankDetails()) {
      reviewSections[ClaimReviewSection.accountDetails] =
          reviewSections[ClaimReviewSection.accountDetails]
            ?..add(ClaimReviewDataItem(
                account_number,
                false,
                FNOLPage.PayoutModule.index.toString(),
                claimRequest.bankDetails?.bankAccountNumber,
                EventValues.cta_edit_bank_details));
    }

    return reviewSections;
  }

  Future<String?> getPaymentLink(PolicyRecurringPaymentDetails policy) async {
    RecurringPaymentLink link =
        await _recurringPaymentRepository.getPaymentLink(policy);
    if (link.paymentLink != null) {
      return Future.value(link.paymentLink);
    } else {
      return Future.value(null);
    }
  }
}
