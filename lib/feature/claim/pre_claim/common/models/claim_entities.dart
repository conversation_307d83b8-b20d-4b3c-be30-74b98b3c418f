import 'dart:math';

import 'package:acko_flutter/common/model/BaseModel.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/models/bank_details.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/models/doc_upload_entity.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/models/treatment_entity.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/view/selfie_page.dart';
import 'package:acko_flutter/feature/health_policy/model/health_policy_model.dart';
import 'package:acko_flutter/feature/payment/payout/models/payout_models.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:intl/intl.dart';
import 'package:policy_details/policy_details.dart';

import '../../../../../common/util/strings.dart';
import 'fnol_cover_page_entity.dart';
import 'health_policies.dart';
import 'member_selection_entity.dart';

////Created by saurabh.lahoti on 11/05/21

class ClaimRequest extends BaseModel {
  static const CLAIM_AMOUNT_BAR = 100000;
  Map<DocumentHeader, List<Document>>? claimBills;
  KYCDetails? kycDetails;
  BankDetails? bankDetails;
  double? claimAmount;
  String? email;
  String? communicationEmail;
  int? claimId;
  int? acrId;
  DateTime? date;
  int? policyId;
  bool? skippedEmail = false;
  bool? disclaimerCompleted = false;
  int? selectedMemberIndex;
  DocumentConfig? documentConfig;
  PayoutResponse? payoutResponse;
  bool? payoutConsent;
  MemberSelection? memberSelection;
  Treatment? treatment;
  HealthPlanPolicies? selectedHealthPlanPolicy;
  int? prePostClaimId;
  String? prePostSubClaimId;
  String? incidentReason;
  String? prePostClaimantUhid;

  ClaimRequest(
      {this.claimBills,
      this.kycDetails,
      this.claimAmount,
      this.claimId,
      this.date,
      this.policyId,
      this.email,
      this.skippedEmail,
      this.disclaimerCompleted,
      this.communicationEmail,
      this.selectedMemberIndex,
      this.bankDetails,
      this.documentConfig,
      this.payoutResponse,
      this.memberSelection,
      this.payoutConsent,
      this.treatment,
      this.selectedHealthPlanPolicy,
      this.prePostClaimId,
      this.prePostSubClaimId,
      this.incidentReason,
      this.prePostClaimantUhid});

  ClaimRequest.error(error) {
    this.error = error;
  }

  bool validateBankDetails() {
    return bankDetails != null &&
        bankDetails!.bankAccountNumber != null &&
        bankDetails!.bankAccountNumberReenter != null &&
        bankDetails!.ifsc != null;
  }

  static ClaimRequest? fromMap(Map<String, dynamic> map) {
    List<MemberHealthPolicy> members = [];
    if (map['members'] != null) {
      map['members'].forEach((v) {
        members.add(MemberHealthPolicy.fromJson(v));
      });
    }
    return ClaimRequest(
        claimBills: _getDocMapFromJson(map['claimBills']),
        kycDetails: KYCDetails.fromMap(map['kycDetails']),
        claimAmount:
            map['claimAmount'] != null ? map['claimAmount'] as double? : null,
        claimId: map['claimId'] != null ? map['claimId'] as int? : null,
        policyId: map['policyId'],
        email: map['email'],
        skippedEmail: map['skippedEmail'],
        disclaimerCompleted: map['disclaimerCompleted'],
        communicationEmail: map['communicationEmail'],
        memberSelection: map['memberSelection'] != null
            ? MemberSelection.fromJson(map['memberSelection'])
            : null,
        documentConfig: map['documentConfig'] != null
            ? DocumentConfig.fromJson(map['documentConfig'])
            : null,
        payoutConsent: map['payout_consent_different_member'],
        date: map['date'] != null
            ? Util.convertUTCToDateTime(map['date'] as String)
            : null,
        bankDetails: map['bankDetails'] != null
            ? BankDetails.fromMap(map['bankDetails'])
            : null,
        payoutResponse: map['payoutResponse'] != null
            ? PayoutResponse.fromJson(map['payoutResponse'])
            : null,
        selectedHealthPlanPolicy: map['selectedPolicy'] != null
            ? HealthPlanPolicies.fromJson(map['selectedPolicy'])
            : null,
        incidentReason: map['incidentReason'],
        treatment: map['treatment'] != null
            ? Treatment.fromJson(map['treatment'])
            : null,
        prePostClaimId: map['parentClaimId'],
        prePostSubClaimId: map['parentSubClaim_id'],
        prePostClaimantUhid: map['claimantUhid'],
        selectedMemberIndex: map['selectedMemberIndex'] != null
            ? map['selectedMemberIndex'] as int?
            : null);
  }

  Map<String, dynamic> toMap() {
    return {
      'claimBills': _convertDocMapToJson(claimBills),
      'kycDetails': kycDetails?.toMap(),
      'claimAmount': this.claimAmount,
      'claimId': this.claimId,
      'policyId': policyId,
      'disclaimerCompleted': disclaimerCompleted,
      'email': email,
      'communicationEmail': communicationEmail,
      'skippedEmail': skippedEmail,
      'incidentReason': incidentReason,
      'bankDetails': bankDetails?.toMap(),
      'payoutResponse': this.payoutResponse?.toJson(),
      'date': this.date != null ? Util.convertDateTimeToUTC(this.date!) : null,
      'selectedMemberIndex': this.selectedMemberIndex,
      'documentConfig': this.documentConfig?.toJson(),
      'payout_consent_different_member': this.payoutConsent,
      'memberSelection': memberSelection?.toJson(),
      'treatment': treatment?.toJson(),
      'selectedPolicy': selectedHealthPlanPolicy?.toJson(),
      'parentClaimId': prePostClaimId,
      'parentSubClaimId': prePostSubClaimId,
      'claimantUhid': prePostClaimantUhid
    };
  }

  Map<String, dynamic> getRequestMap(bool patchRequest) {
    var map = {
      "admission_date": DateFormat('yyyy-M-dd').format(date!),
      "claim_amount": claimAmount.toString(),
      "claim_email": communicationEmail,
      "cover_category_name": memberSelection?.selectedCover?.coverKey,
      "category_type": memberSelection?.selectedCover?.categoryType,
      // "claimant_uhid": memberSelection?.selectedMember?.allPolicies?[0]?.uhid,
      // "proposer_uhid": memberSelection?.proposer?.allPolicies?[0]?.uhid,
      "claimant_uhid": (memberSelection?.selectedCover?.categoryType
                  ?.equalsIgnoreCase("OPD") ??
              false)
          ? getUhidForInsured(memberSelection?.selectedMember,
              policyData:
                  extractOpdPolicy(memberSelection?.selectedMember?.policies))
          : getUhidForInsured(memberSelection?.selectedMember),
      "proposer_uhid": (memberSelection?.selectedCover?.categoryType
                  ?.equalsIgnoreCase("OPD") ??
              false)
          ? getUhidForInsured(memberSelection?.proposer,
              policyData: extractOpdPolicy(memberSelection?.proposer?.policies))
          : getUhidForInsured(memberSelection?.proposer),
      if (payoutConsent != null)
        "payout_consent_different_member": payoutConsent,
      if (prePostClaimId != null) "parent_claim_id": prePostClaimId,
      if (selectedHealthPlanPolicy?.proposerUhid.isNotNullOrEmpty ?? false)
        "proposer_uhid": selectedHealthPlanPolicy?.proposerUhid,
      if (selectedHealthPlanPolicy?.claimantUhid.isNotNullOrEmpty ?? false)
        "claimant_uhid": selectedHealthPlanPolicy?.claimantUhid,
      if (prePostSubClaimId != null) "parent_sub_claim_id": prePostSubClaimId,
      if (prePostClaimantUhid != null) "claimant_uhid": prePostClaimantUhid
    };
    return map;
  }

  HealthPolicyDetailsModel? extractOpdPolicy(
      List<HealthPolicyDetailsModel?>? allPolicies) {
    try {
      return allPolicies?.firstWhere((element) => (element?.productType
              .equalsIgnoreCase(HealthConstants.ENTERPRISE_PRODUCT) ??
          false));
    } catch (e) {
      return allPolicies?[0];
    }
  }

  Map getACRMap() {
    var map = <String, dynamic>{"estimated_amount": claimAmount?.toInt()};
    if (date != null)
      map["admission_date"] = DateFormat('yyyy-MM-dd').format(date!);
    String? claimantUhid =
        getUhidForInsured(memberSelection?.selectedMember, isAcr: true);
    String? proposerUhid =
        getUhidForInsured(memberSelection?.proposer, isAcr: true);
    ;
    if (claimantUhid != null) map["claimant_uhid"] = claimantUhid;
    if (proposerUhid != null) map["proposer_uhid"] = proposerUhid;
    return map;
  }

  String? getUhidForInsured(HealthAssetDetailsResponseInsured? selectedUser,
      {HealthPolicyDetailsModel? policyData = null, bool isAcr = false}) {
    String? uhid;
    if (policyData != null) {
      policyData.insureds?.forEach((insured) {
        if (insured.name.equalsIgnoreCase(selectedUser?.name)) {
          uhid = insured.uhid;
        }
      });
    } else {
      selectedUser?.policies?.forEach((policy) {
        policy.insureds?.forEach((insured) {
          if (insured.relationship
              .equalsIgnoreCase(selectedUser.relationship)) {
            if ((!isAcr ||
                    (isAcr &&
                        policy.productType.equalsIgnoreCase(
                            HealthConstants.ENTERPRISE_PRODUCT))) &&
                uhid.isNullOrEmpty) uhid = insured.uhid;
          }
        });
      });
    }
    return uhid;
  }

  String getEmail() {
    String val = "";
    if (email != null) val += email!;
    if (val.trim().isNotEmpty && communicationEmail != null) val += "\n";
    if (communicationEmail != null) val += communicationEmail!;
    return val;
  }

  bool validate() =>
      claimAmount != null &&
          claimAmount! > 0 &&
          date != null &&
          memberSelection?.maxSumInsured == null ||
      (memberSelection?.maxSumInsured != null &&
              claimAmount! <= memberSelection!.maxSumInsured) &&
          memberSelection?.selectedMember != null &&
          memberSelection!.selectedCover?.coverKey != null;
}

List<Map<String, dynamic>>? _convertDocMapToJson(
    Map<DocumentHeader, List<Document>>? docMap) {
  if (docMap == null) return null;
  List<Map<String, dynamic>> items = [];
  docMap.entries.forEach((entry) {
    List<Map<String, dynamic>> docs = [];
    entry.value.forEach((doc) {
      if (doc.docStatus == null ||
          (doc.docStatus != DocStatus.ERROR &&
              doc.docStatus != DocStatus.UPLOADING)) docs.add(doc.toMap());
    });
    var map = entry.key.toMap();
    if (docs.isEmpty) {
      map['numOfFiles'] = 0;
      docs.add(Document(entry.key.type, entry.key.type! + "_0").toMap());
    }
    items.add({"key": map, "value": docs});
  });
  return items;
}

Map<DocumentHeader, List<Document>>? _getDocMapFromJson(List<dynamic>? json) {
  if (json == null) return null;
  Map<DocumentHeader, List<Document>> map = {};
  json.forEach((element) {
    List<Document> docs = [];
    ((element as Map<String, dynamic>)['value'] as List).forEach((e) {
      docs.add(Document.fromMap(e));
    });
    DocumentHeader header = DocumentHeader.fromMap(element['key']);
    header.numOfFiles = min(docs.length, header.numOfFiles);
    map.addEntries([MapEntry(header, docs)]);
  });
  return map;
}

class ClaimReason {
  String? reason;
  String? key;
  String? dateTitle;
  Map<DocumentHeader, List<Document>>? documents;

  ClaimReason(this.reason, this.key, this.dateTitle);

  factory ClaimReason.fromFirebaseJson(Map<String, dynamic> map) {
    List<MapEntry<DocumentHeader, List<Document>>> entries = [];
    ClaimReason reason = new ClaimReason(map['reason'] as String?,
        map['key'] as String?, map['dateTitle'] as String?);
    (map['documents'] as List).forEach((e) => entries.add(
        MapEntry(DocumentHeader.fromMapAndSize(e, map['maxFileSize']), [])));
    reason.documents = Map.fromEntries(entries);
    return reason;
  }

  static ClaimReason? fromSavedJson(Map<String, dynamic>? map) {
    if (map == null) return null;
    ClaimReason reason = new ClaimReason(map['reason'] as String?,
        map['key'] as String?, map['dateTitle'] as String?);
    reason.documents = _getDocMapFromJson(map['documents']);
    return reason;
  }

  Map<String, dynamic> toMap() {
    return {
      'reason': this.reason,
      'key': this.key,
      'dateTitle': this.dateTitle,
      'documents': _convertDocMapToJson(this.documents),
    };
  }
}

class ClaimReviewDataItem {
  String? title;
  bool isDocument;
  String editIndex;
  bool isCta = false;
  String? description;
  String eventType;

  ClaimReviewDataItem(this.title, this.isDocument, this.editIndex,
      this.description, this.eventType,
      {this.isCta = false});
}

class ReviewData {
  String? title;
  String? description;
  bool? isDocuments = false;
  List<String>? urlDocuments;

  ReviewData({
    this.title,
    this.description,
    this.isDocuments,
    this.urlDocuments,
  });
}

class KYCDetails {
  String? panCard;
  Map<DocumentHeader, List<Document>>? kycDocuments;

  KYCDetails({this.panCard, this.kycDocuments});

  static KYCDetails? fromMap(dynamic map) {
    if (map == null) return null;
    return KYCDetails(
        panCard: map['panCard']?.toString(),
        kycDocuments: map['kycDocuments'] != null
            ? _getDocMapFromJson(map['kycDocuments'])
            : null);
  }

  Map<String, dynamic> toMap() {
    return {
      'panCard': panCard,
      'kycDocuments': _convertDocMapToJson(kycDocuments)
    };
  }

  KYCState getCurrentState() {
    if (kycDocuments == null || validateKYCPage(KYCState.DOC) == null) {
      return KYCState.SELFIE;
    } else
      return KYCState.DOC;
  }

  String? validateKYCPage(KYCState state) {
    String? error;
    switch (state) {
      case KYCState.DOC:
        if (panCard != null && panCard!.length == 10) {
          if (RegExp(HealthConstants.PAN_REGEX).hasMatch(panCard!)) {
            if (kycDocuments != null) {
              bool valid = false;
              kycDocuments?.forEach((key, value) {
                int count = 0;
                if (valid) return;
                if (key.type != DocumentTypes.SELFIE) {
                  value.forEach((element) {
                    if (element.docStatus == DocStatus.UPLOADED) count++;
                  });
                  valid = count == key.pages?.length;
                }
              });
              if (!valid) {
                error = "Upload KYC Documents";
              }
            } else {
              error = "Upload KYC Documents";
            }
          } else {
            error = "Invalid PAN card number";
          }
        } else {
          error = pan_card_error_text;
        }
        break;
      case KYCState.SELFIE:
        if (kycDocuments != null) {
          bool valid = false;
          kycDocuments?.forEach((key, value) {
            if (key.type == DocumentTypes.SELFIE) {
              valid = value.firstOrNull?.docStatus == DocStatus.UPLOADED;
              return;
            }
          });
          if (!valid) error = "Upload KYC selfie";
        }
        break;
    }
    return error;
  }
}
