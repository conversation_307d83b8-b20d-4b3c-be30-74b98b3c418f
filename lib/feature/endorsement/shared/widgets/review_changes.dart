import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/acko_text_config.dart';
import 'package:acko_flutter/common/view/dashed_line.dart';
import 'package:acko_flutter/feature/endorsement/acko_doc_upload/bloc/acko_doc_upload_bloc.dart';
import 'package:acko_flutter/feature/endorsement/acko_doc_upload/model/acko_doc_model.dart';
import 'package:acko_flutter/feature/endorsement/acko_doc_upload/view/acko_doc_upload_widget.dart';
import 'package:acko_flutter/feature/endorsement/domain/repository/hl_endorsement_repository.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_repo.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_policy_changes_model.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:design_module/design_module.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';

class ReviewChangesWidget extends StatefulWidget {
  final String title;
  final List<ChangedValues?>? changesData;
  final bool? editable;
  final bool showUploadDoc;
  final bool? checkboxValue;
  final String? proposalId;
  final String? entityReferenceId;
  final bool hasCheckBox;
  final Function(bool)? updateCheckboxValue;
  final Function(AckoDocumentModel? documents)? docUploadSuccess;
  final Function(AckoDocumentModel? documents)? docDeleteSuccess;
  final JourneyType? journeyType;
  final Function? onEditTap;

  const ReviewChangesWidget(
      {super.key,
      required this.title,
      this.editable = true,
      this.changesData,
      this.showUploadDoc = false,
      this.checkboxValue,
      this.proposalId,
      this.entityReferenceId,
      this.hasCheckBox = false,
      this.updateCheckboxValue,
      this.docUploadSuccess,
      this.docDeleteSuccess,
      this.journeyType,
      this.onEditTap});

  @override
  State<ReviewChangesWidget> createState() => _ReviewChangesWidgetState();
}

class _ReviewChangesWidgetState extends State<ReviewChangesWidget> {
  @override
  Widget build(BuildContext context) {
    final rcReviewChangesWidget = HlEndorsementRepository()
        .endorsementCopies
        .endorsement
        ?.shared
        ?.view
        ?.widgets;
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12), color: colorF5F5F5),
      child: Column(
        children: [
          Container(
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: colorE8E8E8, width: 1),
              color: Colors.white,
            ),
            child: Column(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    padding: EdgeInsets.fromLTRB(16, 16, 16, 12),
                    color: colorF9F6FF,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SDUIText(
                          value: _generateDynamicTitle(widget.title),
                          textColor: color121212,
                          textStyle: "lMedium",
                          maxLines: 5,
                        ),
                        if (widget.editable == true)
                          GestureDetector(
                            onTap: () {
                              widget.onEditTap?.call();
                            },
                            child: SDUIText(
                              value: "Edit",
                              textColor: color1B73E8,
                              textStyle: "lSmall",
                              maxLines: 5,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                ListView.separated(
                    shrinkWrap: true,
                    padding: EdgeInsets.zero,
                    physics: NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) => Container(
                          width: MediaQuery.of(context).size.width,
                          padding: EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SDUIText(
                                value: widget.changesData?[index]?.changeTitle,
                                textStyle: "pSmall",
                                textColor: color4B4B4B,
                                maxLines: 5,
                              ),
                              SizedBox(
                                height: 4,
                              ),
                              if ((widget.changesData?[index]?.changeOldValue
                                          .isNotNullOrEmpty ??
                                      false) &&
                                  (widget.changesData?[index]?.changeNewValue
                                          .isNotNullOrEmpty ??
                                      false))
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Flexible(
                                      child: SDUIText(
                                        value:
                                            "${widget.changesData?[index]?.changeOldValue}",
                                        textStyle: "lSmall",
                                        textColor: color121212,
                                        maxLines: 10,
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 4),
                                      child: Icon(
                                        Icons.arrow_forward_rounded,
                                        size: 20,
                                        color: color121212,
                                      ),
                                    ),
                                    Flexible(
                                      child: SDUIText(
                                        value:
                                            "${widget.changesData?[index]?.changeNewValue}",
                                        textStyle: "lSmall",
                                        textColor: color121212,
                                        maxLines: 10,
                                      ),
                                    ),
                                  ],
                                ),
                              if ((widget
                                          .changesData?[index]
                                          ?.updatedMemberTitle
                                          .isNotNullOrEmpty ??
                                      false) &&
                                  (widget
                                          .changesData?[index]
                                          ?.updatedMemberSubtitle
                                          .isNotNullOrEmpty ??
                                      false)) ...[
                                const SizedBox(
                                  height: 4,
                                ),
                                SDUIText(
                                  value:
                                      "${widget.changesData?[index]?.updatedMemberTitle}",
                                  textStyle: "lSmall",
                                  textColor: color040222,
                                  maxLines: 5,
                                ),
                                const SizedBox(
                                  height: 4,
                                ),
                                SDUIText(
                                  value:
                                      "${widget.changesData?[index]?.updatedMemberSubtitle}",
                                  textStyle: "pXSmall",
                                  textColor: color121212,
                                ),
                                if ((widget.changesData?[index]
                                            ?.updatedMemberSubtitle ??
                                        '')
                                    .containsIgnoreCase('self'))
                                  Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(12),
                                    margin: const EdgeInsets.only(
                                      top: 16,
                                    ),
                                    decoration: ShapeDecoration(
                                      color: colorF8F7FC,
                                      shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(8)),
                                    ),
                                    child: AckoTextConfig.i.paragraphXSmall
                                        .text(rcReviewChangesWidget
                                                ?.selfExclusionNote ??
                                            ''),
                                  ),
                              ],
                              if (widget.showUploadDoc &&
                                  widget.changesData?[index]?.documents !=
                                      null) ...[
                                const SizedBox(
                                  height: 16,
                                ),
                                BlocProvider(
                                  create: (context) => AckoDocUploadCubit(
                                      docUploadService: DocUploadService.PPE,
                                      documents:
                                          widget.changesData?[index]?.documents,
                                      proposalId: widget.proposalId,
                                      entityReferenceId:
                                          widget.entityReferenceId),
                                  child: AckoDocUploadWidget(
                                    docUploadSuccess: widget.docUploadSuccess,
                                    journeyType: widget.journeyType,
                                    docDeleteSuccess: widget.docDeleteSuccess,
                                  ),
                                )
                              ],
                            ],
                          ),
                        ),
                    separatorBuilder: (context, index) => Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: DottedLine(
                            dashColor: colorE7E7F0,
                          ),
                        ),
                    itemCount: widget.changesData?.length ?? 0),
              ],
            ),
          ),
          if (widget.hasCheckBox)
            ClipRRect(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
              child: Container(
                width: MediaQuery.of(context).size.width,
                padding: EdgeInsets.fromLTRB(20, 8, 20, 16),
                color: Colors.transparent,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 18.0,
                      width: 18.0,
                      child: Checkbox(
                        activeColor: color0FA457,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(Radius.circular(3.0)),
                        ),
                        checkColor: colorE0E0E8,
                        value: widget.checkboxValue,
                        onChanged: (value) {
                          widget.updateCheckboxValue?.call(value!);
                        },
                      ),
                    ),
                    SizedBox(
                      width: 12,
                    ),
                    Expanded(
                      child: SDUIText(
                        value: rcReviewChangesWidget?.reviewChanges?.note,
                        textColor: color121212,
                        textStyle: "pXSmall",
                        maxLines: 10,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  _generateDynamicTitle(String? title) {
    switch (title) {
      // case "Porting details":
      //   return "Porting details";
      // case "Policy details":
      //   return "Policy details";
      // case "Member details":
      //   return "Member details";
      case "Member addition/removal":
        {
          bool hasAdd = false;
          bool hasRemove = false;

          widget.changesData?.forEach((element) {
            if (element?.changeTitle.containsIgnoreCase("add") ?? false) {
              hasAdd = true;
            }
            if (element?.changeTitle.containsIgnoreCase("remove") ?? false) {
              hasRemove = true;
            }
          });

          if (hasAdd && hasRemove) {
            return "Member addition and removal";
          } else if (hasAdd) {
            return "Member addition";
          } else if (hasRemove) {
            return "Member removal";
          } else {
            return "Member addition/removal";
          }
        }
      default:
        return title;
    }
  }
}
