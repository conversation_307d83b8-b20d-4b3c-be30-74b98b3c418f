import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/input_field_controllers.dart';
import 'package:acko_flutter/common/util/input_field_extension.dart';
import 'package:acko_flutter/common/view/selector.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_constants.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_utils.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_form_editing_models.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_self_exclusion_details_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/validation_models/family_constrains.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/bottomsheets/ppe_child_not_covered_sheet.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/bottomsheets/ppe_non_editable_fields_sheet.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/bottomsheets/ppe_remove_member_sheet.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:design_module/design_module.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:sdui/sdui.dart';

class MemberDetailsForm extends StatefulWidget {
  final MemberDetails? newMemberDetails;
  final MemberDetails? oldMemberDetails;
  final int memberIndex;
  final Function(String key, String insuredId, String newValue, bool isProposer,
      bool hasError, bool isFinancial)? updateMemberDetails;
  final Function(String insuredId)? addMemberBack;
  final Function(String insuredId)? removeMember;
  final EndorsementFormValidatorConfig validationConfig;
  final bool enableAddBack;
  final bool isRenewalFlow;
  final PpeSelfExclusionDetailsModel? selfExclusionDetails;

  MemberDetailsForm(
      {this.oldMemberDetails,
      this.newMemberDetails,
      required this.memberIndex,
      required this.updateMemberDetails,
      required this.addMemberBack,
      required this.removeMember,
      required this.validationConfig,
      this.selfExclusionDetails,
      this.enableAddBack = true,
      this.isRenewalFlow = false});

  @override
  _MemberDetailsFormState createState() => _MemberDetailsFormState();
}

class _MemberDetailsFormState extends State<MemberDetailsForm> {
  final NameEditingController _nameController = NameEditingController();
  final HeightEditingController _heightController = HeightEditingController();
  final WeightEditingController _weightController = WeightEditingController();
  final PickerEditingController _genderController = PickerEditingController();
  final PickerEditingController _relationshipController =
      PickerEditingController();
  final EmailEditingController _emailController = EmailEditingController();
  final PhoneNumberEditingController _phoneNoController =
      PhoneNumberEditingController();
  final DateEditingController _dateController = DateEditingController();
  final PincodeEditingController _pinCodeController =
      PincodeEditingController();

  bool isProposer = false;
  bool isRemoved = false;
  bool isNewlyAdded = false;

  final HealthJourneyManagerUtils _healthJMUtil = HealthJourneyManagerUtils();

  List<String> disabledFields = [];

  @override
  void initState() {
    super.initState();
    _initializeAndUpdateStyles();
    _genderController.text = _genderController.text.toSentenceCase();
  }

  void _initializeAndUpdateStyles() {
    /// disable fields
    if (widget.newMemberDetails?.height == null) disabledFields.add('height');
    if (widget.newMemberDetails?.weight == null) disabledFields.add('weight');

    if (widget.isRenewalFlow &&
        widget.newMemberDetails?.isNewlyAdded != null &&
        !widget.newMemberDetails!.isNewlyAdded) {
      if (!disabledFields.contains("height")) {
        disabledFields.add('height');
      }
      if (!disabledFields.contains("weight")) {
        disabledFields.add('weight');
      }
    }

    isProposer = widget.newMemberDetails?.isProposer ?? false;
    isRemoved = widget.newMemberDetails?.isRemoved ?? false;
    isNewlyAdded = widget.newMemberDetails?.isNewlyAdded ?? false;
    _nameController.text = widget.newMemberDetails?.name ?? '';
    _genderController.text = widget.newMemberDetails?.gender ?? '';
    _relationshipController.text = widget.newMemberDetails?.relation ?? '';
    _dateController.text = widget.newMemberDetails?.dateOfBirth ?? '';
    _heightController.text = widget.newMemberDetails?.height ?? '';
    _weightController.text = widget.newMemberDetails?.weight ?? '';
    _pinCodeController.text = widget.newMemberDetails?.pinCode ?? '';
    _phoneNoController.text = widget.newMemberDetails?.mobileNumber ?? '';
    _emailController.text = widget.newMemberDetails?.email ?? '';
    _nameController.inputTextStyle = _getFieldTextStyle(
      _nameController.text,
      widget.oldMemberDetails?.name,
      widget.newMemberDetails?.name,
      widget.newMemberDetails?.isNewlyAdded ?? false,
      widget.newMemberDetails?.isRemoved ?? false,
    );
    _genderController.inputTextStyle = _getFieldTextStyle(
      _genderController.text,
      widget.oldMemberDetails?.gender,
      widget.newMemberDetails?.gender,
      widget.newMemberDetails?.isNewlyAdded ?? false,
      widget.newMemberDetails?.isRemoved ?? false,
    );
    _relationshipController.inputTextStyle = _getFieldTextStyle(
      _relationshipController.text,
      widget.oldMemberDetails?.relation,
      widget.newMemberDetails?.relation,
      widget.newMemberDetails?.isNewlyAdded ?? false,
      true || (widget.newMemberDetails?.isRemoved ?? false),

      /// true added to disable relationship field will need to remove this in future when relationship edit is supported from App for the feature
    );
    _dateController.inputTextStyle = _getFieldTextStyle(
      _dateController.text,
      widget.oldMemberDetails?.dateOfBirth,
      widget.newMemberDetails?.dateOfBirth,
      widget.newMemberDetails?.isNewlyAdded ?? false,
      widget.newMemberDetails?.isRemoved ?? false,
    );
    _heightController.inputTextStyle = _getFieldTextStyle(
      _heightController.text,
      widget.oldMemberDetails?.height,
      widget.newMemberDetails?.height,
      widget.newMemberDetails?.isNewlyAdded ?? false,
      widget.newMemberDetails?.isRemoved ?? false,
    );
    _weightController.inputTextStyle = _getFieldTextStyle(
      _weightController.text,
      widget.oldMemberDetails?.weight,
      widget.newMemberDetails?.weight,
      widget.newMemberDetails?.isNewlyAdded ?? false,
      widget.newMemberDetails?.isRemoved ?? false,
    );
    _pinCodeController.inputTextStyle = _getFieldTextStyle(
      _pinCodeController.text,
      widget.oldMemberDetails?.pinCode,
      widget.newMemberDetails?.pinCode,
      widget.newMemberDetails?.isNewlyAdded ?? false,
      widget.isRenewalFlow ? true : widget.newMemberDetails?.isRemoved ?? false,
    );
    if ((widget.oldMemberDetails?.isProposer ?? false) ||
        (widget.newMemberDetails?.isProposer ?? false)) {
      _phoneNoController.inputTextStyle = _getFieldTextStyle(
        _phoneNoController.text,
        widget.oldMemberDetails?.mobileNumber,
        widget.newMemberDetails?.mobileNumber,
        widget.newMemberDetails?.isNewlyAdded ?? false,
        widget.newMemberDetails?.isRemoved ?? false,
      );
      _emailController.inputTextStyle = _getFieldTextStyle(
        _emailController.text,
        widget.oldMemberDetails?.email,
        widget.newMemberDetails?.email,
        widget.newMemberDetails?.isNewlyAdded ?? false,
        widget.isRenewalFlow
            ? widget.newMemberDetails?.isRemoved ?? false
            : true,

        /// currently not supported by PPE App journey
      );
    }
  }

  TextStyle _getFieldTextStyle(String currentValue, String? oldValue,
      String? newValue, bool isNewlyAdded, bool isDisabled) {
    final sanitizedCurrentValue = _healthJMUtil.sanitizeValue(currentValue);
    final sanitizedOldValue = _healthJMUtil.sanitizeValue(oldValue ?? '');
    final sanitizedNewValue = _healthJMUtil.sanitizeValue(newValue ?? '');

    if (isDisabled) {
      return _healthJMUtil.disabledFieldStyle;
    } else if ((sanitizedCurrentValue != sanitizedOldValue ||
            sanitizedCurrentValue != sanitizedNewValue) &&
        !isNewlyAdded) {
      return _healthJMUtil.changedFieldStyle;
    } else {
      return _healthJMUtil.inputFieldStyle;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _genderController.dispose();
    _relationshipController.dispose();
    _phoneNoController.dispose();
    _emailController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    _dateController.dispose();
    _pinCodeController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SDUIImage(
              imageUrl: HealthJourneyManagerAssets.memberIcon,
              width: 24,
              height: 24,
            ),
            const SizedBox(
              width: 8,
            ),
            SDUIText(
              value: widget.selfExclusionDetails?.memberTitle ??
                  "Member ${widget.memberIndex}" +
                      (isProposer ? " (Proposer)" : ""),
              maxLines: 10,
              textStyle: "lMedium",
              textColor: widget.newMemberDetails?.isNewlyAdded ?? false
                  ? color0FA457
                  : color040222,
              alignment: TextAlign.start,
            ),
            if (widget.newMemberDetails?.isNewlyAdded ?? false)
              SDUIText(
                margin: EdgeInsets.only(left: 4),
                value: "Added",
                textStyle: 'lXXSmall',
                textConfiguration: TextConfiguration.colored,
                padding: const EdgeInsets.all(4),
                border: WidgetBorder(cornerRadius: 6),
                alignment: TextAlign.center,
                textColor: color0B753E,
                background: WidgetBackground(color: colorEBFBEE),
              ),
            if (widget.selfExclusionDetails?.removal == true && !isRemoved) ...[
              Spacer(),
              GestureDetector(
                onTap: () {
                  context.showAckoModalBottomSheet(
                      child: PrePolicyEditsRemoveMemberSheet(
                          memberName: widget.newMemberDetails?.name ?? "",
                          removeMember: widget.removeMember,
                          insuredId: widget.newMemberDetails?.insuredId ?? ""));
                },
                child: SDUIText(
                  value: "Remove",
                  textStyle: "lSmall",
                  textColor: colorD83D37,
                ),
              ),
            ]
          ],
        ),
        if (isRemoved) ...[
          const SizedBox(height: 16),
          Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  begin: Alignment(0.99, -0.15),
                  end: Alignment(-0.99, 0.15),
                  colors: [Color(0xFFD6F9DC), Color(0x00DFFFE5)],
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.info_outline_rounded,
                    size: 20,
                    color: color36354C,
                  ),
                  SizedBox(
                    width: 8,
                  ),
                  Expanded(
                      child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SDUIText(
                        value:
                            "${widget.newMemberDetails?.name ?? ""} will be removed from the policy after the changes are submitted.",
                        textStyle: "pXSmall",
                        textColor: color040222,
                        alignment: TextAlign.start,
                        maxLines: 10,
                      ),
                      if (widget.enableAddBack) ...[
                        GestureDetector(
                          onTap: () {
                            widget.addMemberBack?.call(
                                widget.newMemberDetails?.insuredId ?? "");
                          },
                          child: SDUIText(
                            value: "Add back",
                            textStyle: "lXSmall",
                            textColor: color1B73E8,
                            alignment: TextAlign.start,
                          ),
                        ),
                      ],
                    ],
                  ))
                ],
              )),
        ],
        const SizedBox(
          height: 20,
        ),
        _getNameInputField(),
        SizedBox(height: (isProposer) ? 24 : 16),
        if (!isProposer) ...[
          _getRelationshipInputField(),
          const SizedBox(height: 16),
        ],
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(child: _getDateOfBirthInputField()),
            const SizedBox(width: 20),
            Flexible(child: _getGenderInputField()),
          ],
        ),
        if (_dateController.errorText.isNotNullOrEmpty) ...[
          /// DOB Error text
          const SizedBox(
            height: 4,
          ),
          SDUIText(
            value: _dateController.errorText ?? "",
            textStyle: "pXSmall",
            textColor: colorD83D37,
            alignment: TextAlign.start,
            maxLines: 5,
          )
        ],
        const SizedBox(height: 16),
        if (!disabledFields.contains("height") &&
            !disabledFields.contains("weight")) ...[
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!disabledFields.contains("height")) ...[
                Flexible(child: _getHeightInputField()),
                const SizedBox(width: 20),
              ],
              if (!disabledFields.contains("weight"))
                Flexible(child: _getWeightInputField()),
            ],
          ),
          const SizedBox(height: 16),
        ],
        if (isProposer) ...[
          _getPincodeInputField(),
          const SizedBox(height: 16),
          _getPhoneNumberInputField(),
          const SizedBox(height: 16),
          if (widget.isRenewalFlow) ...[
            _getEmailInputField()
          ] else ...[
            _getDisabledEmailInputField()
          ]
        ],
      ]),
    );
  }

  _getNameInputField() => AckoTextFormField().textInputField(
        placeholder: "Name",
        controller: _nameController,
        onChanged: (value) {
          setState(() {
            _nameController.errorText = _nameController.errorText;
            if (_nameController.errorText.isNotNullOrEmpty) {
              _nameController.inputTextStyle = _healthJMUtil.errorFieldStyle;
            } else if (_nameController.text != widget.oldMemberDetails?.name)
              _nameController.inputTextStyle = _healthJMUtil.changedFieldStyle;
            else
              _nameController.inputTextStyle = _healthJMUtil.inputFieldStyle;
          });
          widget.updateMemberDetails?.call(
              "name",
              widget.newMemberDetails?.insuredId ?? '',
              _nameController.text,
              widget.newMemberDetails?.isProposer ?? false,
              _nameController.errorText.isNotNullOrEmpty,
              false);
        },
        errorText: _nameController.errorText,
        hintText: widget.selfExclusionDetails?.note ??
            (isProposer
                ? "Note: Your policy will be issued in this name."
                : null),
        state: isRemoved
            ? AckoInputFieldState.disabled
            : AckoInputFieldState.active,
      );

  _getRelationshipInputField() => GestureDetector(
        onTap: () {
          if (isRemoved) return;

          context.showAckoModalBottomSheet(
              child: NonEditableFieldInfoSheet(
            fieldName: "relationship",
          ));
        },
        child: AckoTextFormField().pickerInputField(
          placeholder: "Relationship",
          controller: _relationshipController,
          onTap: () {
            context.showAckoModalBottomSheet(
                child: NonEditableFieldInfoSheet(
              fieldName: "relationship",
            ));
            // todo NOTE: relationship change will be part of v2
            // showBottomSheetPicker(
            //   _healthJMUtil.getRelationShipList(),
            //   context,
            //   false,
            //   dropDownHeading: 'Relationship',
            //   onValueChanged: (val) {
            //     // Handle gender selection
            //     _relationshipController.text = val.name!.toLowerCase();
            //     widget.cubit?.findAndUpdateMemberDetails(
            //         key: "relation",
            //         insuredId: widget.newMemberDetails?.insuredId,
            //         newValue: _relationshipController.text,
            //         isProposer: widget.newMemberDetails?.isProposer ?? false);
            //
            //     setState(() {
            //       // _genderController.errorText = _genderController.errorText;
            //       if (_relationshipController.text !=
            //               widget.oldMemberDetails?.relation ||
            //           _relationshipController.text !=
            //               widget.newMemberDetails
            //                   ?.relation)
            //         _relationshipController.inputTextStyle =
            //             changedFieldStyle; // TextStyleInterMediumL1(color: color0FA457, fontSize: 14.0);
            //       else
            //         _relationshipController.inputTextStyle =
            //             unchangedFieldStyle; // TextStyleInterMediumL1(color: color040222, fontSize: 14.0);
            //     });
            //
            //     // _checkAndUpdateCtaStatus(); // Uncomment if you have this method
            //   },
            // );
          },
          suffixIcon: Icon(Icons.keyboard_arrow_down_sharp,
              color: color121212.withOpacity(
                  0.4) // isRemoved ? color121212.withOpacity(0.4) : color36354C,
              ),
          state: AckoInputFieldState
              .disabled, // isRemoved ? AckoInputFieldState.disabled : AckoInputFieldState.active,
        ),
      );

  _getDateOfBirthInputField() {
    /// not passing error text from here but it might result in border not setting to red...
    return AckoTextFormField().dateInputField(
      onTap: _dobOnTap,
      controller: _dateController,
      placeholder: "Date of birth",
      errorText: _dateController.errorText,
      disableErrorMessage: true,
      state:
          isRemoved ? AckoInputFieldState.disabled : AckoInputFieldState.active,
    );
  }

  _getGenderInputField() => AckoTextFormField().pickerInputField(
        placeholder: "Gender",
        controller: _genderController,
        onTap: () {
          showBottomSheetPicker(_healthJMUtil.getGenderList(), context, false,
              dropDownHeading: 'Gender', onValueChanged: (val) {
            _genderController.text = val.name.toSentenceCase();
            widget.updateMemberDetails?.call(
                "gender",
                widget.newMemberDetails?.insuredId ?? '',
                _genderController.text.toLowerCase(),
                widget.newMemberDetails?.isProposer ?? false,
                _genderController.errorText.isNotNullOrEmpty,
                false);
            setState(() {
              if (_genderController.text
                  .notEqualsIgnoreCase(widget.oldMemberDetails?.gender))
                _genderController.inputTextStyle =
                    _healthJMUtil.changedFieldStyle;
              else
                _genderController.inputTextStyle =
                    _healthJMUtil.inputFieldStyle;
            });
          }, height: 0.22);
        },
        suffixIcon: Icon(
          Icons.keyboard_arrow_down_sharp,
          color: color36354C,
        ),
        state: isRemoved
            ? AckoInputFieldState.disabled
            : AckoInputFieldState.active,
      );

  _getHeightInputField() => AckoTextFormField().heightInputField(
        placeholder: "Height",
        controller: _heightController,
        onChanged: (value) {
          widget.updateMemberDetails?.call(
              "height",
              widget.newMemberDetails?.insuredId ?? '',
              _heightController.text,
              widget.newMemberDetails?.isProposer ?? false,
              _heightController.errorText.isNotNullOrEmpty,
              false);
          setState(() {
            _heightController.errorText = _heightController.errorText;
            if (_heightController.errorText.isNotNullOrEmpty) {
              _heightController.inputTextStyle = _healthJMUtil.errorFieldStyle;
            } else if (_heightController.text !=
                widget.oldMemberDetails?.height)
              _heightController.inputTextStyle =
                  _healthJMUtil.changedFieldStyle;
            else
              _heightController.inputTextStyle = _healthJMUtil.inputFieldStyle;
          });
        },
        errorText: _heightController.errorText,
        state: isRemoved
            ? AckoInputFieldState.disabled
            : AckoInputFieldState.active,
      );

  _getWeightInputField() => AckoTextFormField().weightInputField(
        placeholder: "Weight",
        controller: _weightController,
        onChanged: (value) {
          widget.updateMemberDetails?.call(
              "weight",
              widget.newMemberDetails?.insuredId ?? '',
              _weightController.text,
              widget.newMemberDetails?.isProposer ?? false,
              _weightController.errorText.isNotNullOrEmpty,
              false);
          setState(() {
            _weightController.errorText = _weightController.errorText;
            if (_weightController.errorText.isNotNullOrEmpty) {
              _weightController.inputTextStyle = _healthJMUtil.errorFieldStyle;
            } else if (_weightController.text !=
                widget.oldMemberDetails?.weight)
              _weightController.inputTextStyle =
                  _healthJMUtil.changedFieldStyle;
            else
              _weightController.inputTextStyle = _healthJMUtil.inputFieldStyle;
          });
        },
        errorText: _weightController.errorText,
        state: isRemoved
            ? AckoInputFieldState.disabled
            : AckoInputFieldState.active,
      );

  _getPincodeInputField() {
    if (widget.isRenewalFlow) {
      return GestureDetector(
        onTap: () {
          context.showAckoModalBottomSheet(
              child: NonEditableFieldInfoSheet(
            fieldName: "pincode",
          ));
        },
        child: AckoTextFormField().pincodeInputField(
            controller: _pinCodeController,
            placeholder: "Pin code",
            state: AckoInputFieldState.disabled,
            onTap: () {
              context.showAckoModalBottomSheet(
                  child: NonEditableFieldInfoSheet(
                fieldName: "Pin code",
              ));
            }),
      );
    } else {
      return AckoTextFormField().pincodeInputField(
        placeholder: "Pin code",
        controller: _pinCodeController,
        onChanged: (value) {
          setState(() {
            _pinCodeController.errorText = _pinCodeController.errorText;

            if (_pinCodeController.errorText.isNotNullOrEmpty) {
              _pinCodeController.inputTextStyle = _healthJMUtil.errorFieldStyle;
            } else if (_pinCodeController.text !=
                widget.oldMemberDetails?.pinCode)
              _pinCodeController.inputTextStyle =
                  _healthJMUtil.changedFieldStyle;
            else
              _pinCodeController.inputTextStyle = _healthJMUtil.inputFieldStyle;
          });
          if (_pinCodeController.text.length == 6 &&
              _pinCodeController.errorText.isNullOrEmpty)
            widget.updateMemberDetails?.call(
                "pincode",
                widget.newMemberDetails?.insuredId ?? '',
                _pinCodeController.text,
                widget.newMemberDetails?.isProposer ?? false,
                _pinCodeController.errorText.isNotNullOrEmpty,
                true);
        },
        errorText: _pinCodeController.errorText,
        state: isRemoved
            ? AckoInputFieldState.disabled
            : AckoInputFieldState.active,
      );
    }
  }

  _getPhoneNumberInputField() => GestureDetector(
        onTap: () {
          context.showAckoModalBottomSheet(
              child: NonEditableFieldInfoSheet(
            fieldName: "mobile number",
          ));
        },
        child: AckoTextFormField().phoneNumberInputField(
            controller: _phoneNoController,
            placeholder: "Mobile number",
            state: AckoInputFieldState.disabled,
            onTap: () {
              context.showAckoModalBottomSheet(
                  child: NonEditableFieldInfoSheet(
                fieldName: "mobile number",
              ));
            }),
      );

  _getDisabledEmailInputField() => GestureDetector(
        onTap: () {
          context.showAckoModalBottomSheet(
              child: NonEditableFieldInfoSheet(
            fieldName: "email ID",
          ));
        },
        child: AckoTextFormField().emailInputField(
          controller: _emailController,
          placeholder: "Email ID",
          state: AckoInputFieldState.disabled,
          onTap: () {
            context.showAckoModalBottomSheet(
                child: NonEditableFieldInfoSheet(
              fieldName: "email ID",
            ));
          },
        ),
      );

  _getEmailInputField() => AckoTextFormField().emailInputField(
        placeholder: "Email ID",
        controller: _emailController,
        onChanged: (value) {
          widget.updateMemberDetails?.call(
              "email",
              widget.newMemberDetails?.insuredId ?? '',
              _emailController.text,
              widget.newMemberDetails?.isProposer ?? false,
              _emailController.errorText.isNotNullOrEmpty,
              false);
          setState(() {
            _emailController.errorText = _emailController.errorText;

            if (_emailController.errorText.isNotNullOrEmpty) {
              _emailController.inputTextStyle = _healthJMUtil.errorFieldStyle;
            } else if (_emailController.text != widget.oldMemberDetails?.email)
              _emailController.inputTextStyle = _healthJMUtil.changedFieldStyle;
            else
              _emailController.inputTextStyle = _healthJMUtil.inputFieldStyle;
          });
        },
        errorText: _emailController.errorText,
        state: isRemoved
            ? AckoInputFieldState.disabled
            : AckoInputFieldState.active,
      );

  _dobOnTap() async {
    String dob = _healthJMUtil.convertSlashToHyphen(_dateController.text);
    DateTime initialDate = DateFormat('dd-MM-yyyy').parse(dob);

    DateTime today = DateTime.now();
    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate, // DateTime(today.year, today.month, today.day),
      firstDate: DateTime(today.year - 120, today.month, today.day),
      lastDate: DateTime(today.year, today.month, today.day),
      locale: const Locale('en', 'GB'),
    );

    final String relationship = _relationshipController.text;
    bool isValid = true;
    String? errorMessage;

    if (pickedDate != null) {
      String formattedDate =
          '${pickedDate.day.toString().padLeft(2, '0')}/${pickedDate.month.toString().padLeft(2, '0')}/${pickedDate.year}';

      DateTime threeMonthsAgo = DateTime.now().subtract(Duration(days: 91));

      if (relationship.containsIgnoreCase("child") &&
          pickedDate.isAfter(threeMonthsAgo)) {
        context.showAckoModalBottomSheet(
            child: ChildNotCoveredSheet(
                memberName: widget.newMemberDetails?.name ?? "",
                removeMember: widget.removeMember,
                insuredId: widget.newMemberDetails?.insuredId ?? ""));
        return;
      }

      if (relationship.isNullOrEmpty) {
        errorMessage = 'Select relationship before adding dob';
        setState(() {
          _dateController.inputTextStyle = _healthJMUtil.errorFieldStyle;
          _dateController.errorText = errorMessage;
        });
        return;
      }

      FamilyConstrains? constraint;
      for (var c in widget.validationConfig.familyConstrains) {
        if (c.option.equalsIgnoreCase(relationship)) {
          constraint = c;
          break;
        }
      }

      if (constraint != null && constraint.age != null) {
        DateTime minDate;
        DateTime maxDate;

        switch (constraint.age!.min!.qualifier) {
          case 'calendar':
            minDate = DateTime(
              today.year - (constraint.age!.min!.value?.y ?? 0),
              today.month - (constraint.age!.min!.value?.m ?? 0),
              today.day - (constraint.age!.min!.value?.d ?? 0),
            );
            break;

          case 'day':
            minDate = today.subtract(
                Duration(days: constraint.age!.min!.value?.days ?? 0));
            break;

          default:
            minDate = DateTime(today.year - 100, today.month, today.day);
            break;
        }

        switch (constraint.age!.max!.qualifier) {
          case 'calendar':
            maxDate = DateTime(
              today.year - (constraint.age!.max!.value?.y ?? 0),
              today.month - (constraint.age!.max!.value?.m ?? 0),
              today.day - (constraint.age!.max!.value?.d ?? 0),
            );
            break;

          case 'day':
            maxDate = today.subtract(
                Duration(days: constraint.age!.max!.value?.days ?? 0));
            break;

          default:
            maxDate = DateTime(today.year - 18, today.month, today.day);
            break;
        }
        errorMessage = pickedDate.isAfter(minDate)
            ? constraint.age!.minError
            : pickedDate.isBefore(maxDate)
                ? constraint.age!.maxError
                : null;
        _dateController.text = formattedDate;
        if (errorMessage != null) {
          isValid = false;
          setState(() {
            if (_healthJMUtil.convertSlashToHyphen(_dateController.text) !=
                widget.oldMemberDetails?.dateOfBirth) {
              _dateController.inputTextStyle = _healthJMUtil.errorFieldStyle;
              _dateController.errorText = errorMessage;
            } else {
              _dateController.inputTextStyle = _healthJMUtil.errorFieldStyle;
              _dateController.errorText = errorMessage;
            }
          });
        }
      } else {
        isValid = false;
        errorMessage =
            'No validation rules found for the selected relationship.';
        _dateController.text = formattedDate;
        setState(() {
          if (_healthJMUtil.convertSlashToHyphen(_dateController.text) !=
              widget.oldMemberDetails?.dateOfBirth) {
            _dateController.inputTextStyle = _healthJMUtil.errorFieldStyle;
            _dateController.errorText = errorMessage;
          } else {
            _dateController.inputTextStyle = _healthJMUtil.errorFieldStyle;
            _dateController.errorText = errorMessage;
          }
        });
      }

      if (isValid) {
        _dateController.text = formattedDate;
        widget.updateMemberDetails?.call(
            "dob",
            widget.newMemberDetails?.insuredId ?? '',
            _healthJMUtil.convertSlashToHyphen(_dateController.text),
            widget.newMemberDetails?.isProposer ?? false,
            _dateController.errorText.isNotNullOrEmpty,
            true);
        setState(() {
          if (_healthJMUtil.convertSlashToHyphen(_dateController.text) !=
              widget.oldMemberDetails?.dateOfBirth) {
            _dateController.inputTextStyle = _healthJMUtil.changedFieldStyle;
            _dateController.errorText = null;
          } else {
            _dateController.inputTextStyle = _healthJMUtil.inputFieldStyle;
            _dateController.errorText = null;
          }
        });
      }
    }
  }
}
