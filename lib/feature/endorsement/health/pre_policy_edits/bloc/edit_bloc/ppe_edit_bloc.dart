import 'dart:convert';

import 'package:acko_flutter/common/model/picker_model.dart';
import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_constants.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_instrumentation_mixin.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_utils.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/models/health_jm_response.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_nodes.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_repo.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_form_editing_models.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_policy_changes_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_premium_changes_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/validation_models/family_constrains.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

import 'ppe_edit_states.dart';

class PPEEditCubit extends AckoSafeCubit<PPEEditState>
    with HealthJourneyManagerInstrumentationMixin {
  final _repo = JourneyManagerRepository();
  EditType editType;
  final String proposalId;

  FormValues formValues = FormValues();
  HealthJourneyManagerResponse? prePolicyEditingResponse;
  PrePolicyEditNodes? nextNode;

  double scrollOffset = 0.0;
  bool scrollToBottom;
  String? phoneNumber;
  bool newSkuPlan = false;

  PPEEditCubit(
      {required this.proposalId,
      this.editType = EditType.NONE,
      this.prePolicyEditingResponse,
      this.scrollToBottom = false})
      : super(Initial());

  HealthJourneyManagerUtils _ppeUtils = HealthJourneyManagerUtils();

  Future<void> getEditsData({bool force = false}) async {
    emit(Loading());
    phoneNumber =
        await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);
    if (force || prePolicyEditingResponse == null) {
      Map<String, dynamic> requestBody = {
        "current_node_id": PrePolicyEditNodes.HYDRATE_DETAILS.value,
        "edit": {
          "entity_id": proposalId,
          "journey": "health_pre_policy_edit",
          "entity_type": "proposal"
        },
        "input_data_id": null
      };
      prePolicyEditingResponse =
          await _repo.manageJourney(JourneyType.PRE_POLICY_EDIT, requestBody);
    }

    nextNode = prePolicyEditingResponse?.getHealthJourneyManagerNode();

    if (prePolicyEditingResponse != null &&
        prePolicyEditingResponse?.error == null) {
      newSkuPlan =
          true || prePolicyEditingResponse?.edit?.cohort == 'new_sku_2025';
      formValues.populateFormValuesFromPrePolicyEditsResponse(
          prePolicyEditingResponse?.edit?.oldValue,
          prePolicyEditingResponse?.edit?.packagePlans,
          prePolicyEditingResponse?.edit?.deductibles,
          paymentDate:
              prePolicyEditingResponse?.edit?.entityDetails?.paymentDate,
          isOld: true);
      formValues.populateFormValuesFromPrePolicyEditsResponse(
          prePolicyEditingResponse?.edit?.newValue,
          prePolicyEditingResponse?.edit?.packagePlans,
          prePolicyEditingResponse?.edit?.deductibles,
          paymentDate:
              prePolicyEditingResponse?.edit?.entityDetails?.paymentDate,
          isOld: false);
      formValues.updateMemberStatus();
      formValues.sortNewByOldValues();

      String? familyConstraintsData = RemoteConfigInstance.instance
          .getData(RemoteConfigKeysSet.FAMILY_CONSTRAINS);

      List<Map<String, dynamic>>? parsedList = familyConstraintsData != null
          ? List<Map<String, dynamic>>.from(jsonDecode(familyConstraintsData))
          : null;

      EndorsementFormValidatorConfig? config = parsedList != null
          ? EndorsementFormValidatorConfig.fromJson(parsedList)
          : null;

      formValues.setConfig(
          prePolicyEditingResponse?.edit?.familyConstrains ?? config!);

      int totalEdits = formValues.calculateTotalEdits;
      bool enableCta = totalEdits != 0 && formValues.areAllEditsValid;
      PremiumDetails premiumDetails =
          mapToPremiumDetails(prePolicyEditingResponse?.edit?.deltaPremium);

      emit(Loaded(
          formValues: formValues,
          totalEdits: totalEdits,
          amount: premiumDetails.toBePaid?.toInt(),
          enableCta: enableCta,
          validationConfig:
              prePolicyEditingResponse?.edit?.familyConstrains ?? config!,
          refreshOverview: scrollToBottom,
          showAddMore: (formValues.newValues?.memberDetails?.length ?? 0) + 1 <
              formValues.getMaxMemberLimit()));
      await Future.delayed(Duration(milliseconds: 200));
      emit(AutoScroll(scrollToBottom: scrollToBottom));
      scrollToBottom = false;
    } else {
      emit(Error());
    }
  }

  PremiumDetails mapToPremiumDetails(DeltaPremium? deltaPremium) {
    return PremiumDetails(
      totalPremiumDue: deltaPremium?.newInstallment,
      premiumAlreadyPaid: deltaPremium?.paidAmount,
      toBePaid: deltaPremium?.adhocPayment,
      dueTime: deltaPremium?.premiumPendingCount,
    );
  }

  void getPolicyChangesData() {
    final deltaGrossPremium =
        prePolicyEditingResponse?.edit?.deltaPremium?.deltaGrossPremium;
    PolicyChangesData policyChangesData = new PolicyChangesData();
    policyChangesData.addChangesFromForm(formValues);
    policyChangesData.amountToBePaid(
        prePolicyEditingResponse?.edit?.deltaPremium?.adhocPayment);
    policyChangesData
        .mapToPremiumChange(prePolicyEditingResponse?.edit?.deltaPremium);
    emit(ReviewSheetLoaded(
        policyChangesData: policyChangesData,
        totalEdits: policyChangesData.totalEdits));
  }

  void updatePortingDetails({required String key, dynamic newValue}) {
    editType = EditType.PORTING;
    switch (key) {
      case 'current_policy_expiry_date':
        formValues.newValues?.portingDetails?.portingDate = newValue;
        break;
    }
    updateForNonFinancialEdits();
  }

  /// when sync not required
  updateForNonFinancialEdits({bool hasError = false}) async {
    String? familyConstraintsData = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.FAMILY_CONSTRAINS);

    List<Map<String, dynamic>>? parsedList = familyConstraintsData != null
        ? List<Map<String, dynamic>>.from(jsonDecode(familyConstraintsData))
        : null;

    EndorsementFormValidatorConfig? config = parsedList != null
        ? EndorsementFormValidatorConfig.fromJson(parsedList)
        : null;

    formValues
        .setConfig(prePolicyEditingResponse?.edit?.familyConstrains ?? config!);

    int totalEdits = formValues.calculateTotalEdits;
    bool enableCta =
        totalEdits != 0 && formValues.areAllEditsValid && !hasError;
    PremiumDetails premiumDetails =
        mapToPremiumDetails(prePolicyEditingResponse?.edit?.deltaPremium);
    emit(Loaded(
        formValues: formValues,
        addMemberFlow: false,
        redirectToNextNode: false,
        amount: premiumDetails.toBePaid?.toInt(),
        enableCta: enableCta,
        totalEdits: totalEdits,
        validationConfig:
            prePolicyEditingResponse?.edit?.familyConstrains ?? config!,
        refreshOverview: true,
        showAddMore: (formValues.newValues?.memberDetails?.length ?? 0) + 1 <
            formValues.getMaxMemberLimit()));
    await Future.delayed(Duration(milliseconds: 200));
    emit(AutoScroll());
  }

  void updatePolicyDetails({required String key, dynamic newValue}) {
    editType = EditType.POLICY;

    bool changesMade = true;

    switch (key) {
      case 'plan_name':
        formValues.newValues?.policyDetails?.packageName = newValue;

        /// plan name change not allowed from app
        break;
      case 'sum_insured':
        {
          PickerModel siValues;
          siValues = PickerModel(
              id: newValue ?? '',
              name: HealthJourneyManagerUtils()
                  .formatCurrencyWithUnits(newValue));
          if (formValues.newValues?.policyDetails?.sumInsured?.id ==
              siValues.id) changesMade = false;
          formValues.newValues?.policyDetails?.sumInsured = siValues;
        }
        break;
      case 'deductible':
        {
          PickerModel deductibleValues;
          deductibleValues = PickerModel(
              id: newValue ?? '',
              name: HealthJourneyManagerUtils()
                  .formatCurrencyWithUnits(newValue));
          if (formValues.newValues?.policyDetails?.deductible?.id ==
              deductibleValues.id) changesMade = false;
          formValues.newValues?.policyDetails?.deductible = deductibleValues;
        }
        break;
    }

    if (changesMade) syncApiAndFormValues(nextNode: PrePolicyEditNodes.PREMIUM);

    /// is financial check not required both are financial edits
  }

  void findAndUpdateMemberDetails(
      {required String key,
      dynamic newValue,
      required String? insuredId,
      required bool isProposer,
      bool isFinancial = false,
      bool hasError = false}) {
    editType = EditType.MEMBER;

    if (hasError) {
      updateForNonFinancialEdits(hasError: true);
      return;
    }

    MemberDetails? member;
    if (isProposer) {
      member = formValues.newValues?.proposerDetails;
      if (member != null) {
        MemberDetails updatedProposerDetails =
            updateMemberDetails(member: member, key: key, newValue: newValue);
        formValues.newValues?.proposerDetails = updatedProposerDetails;
      }
    } else {
      member = (formValues.newValues?.memberDetails ?? []).firstWhere(
        (member) => member?.insuredId.equalsIgnoreCase(insuredId) ?? false,
        orElse: () => null,
      );

      if (member != null) {
        final members = formValues.newValues?.memberDetails ?? [];
        final index = members.indexWhere(
            (m) => m?.insuredId?.equalsIgnoreCase(insuredId) ?? false);
        if (index != -1) {
          members.replaceRange(index, index + 1, [
            updateMemberDetails(member: member, key: key, newValue: newValue)
          ]);
        }

        formValues.newValues?.memberDetails = members;
      }
    }

    if (isFinancial) {
      syncApiAndFormValues(nextNode: PrePolicyEditNodes.PREMIUM);
    } else
      updateForNonFinancialEdits();
  }

  MemberDetails updateMemberDetails(
      {required MemberDetails member,
      required String key,
      required dynamic newValue}) {
    /// only editable fields can be changed
    switch (key) {
      case "name":
        member = member.copyWith(name: newValue);
        break;
      case "gender":
        member = member.copyWith(gender: newValue);
        break;
      case "dob":
        member = member.copyWith(dateOfBirth: newValue);
        break;
      case "height":
        member = member.copyWith(height: newValue);
        break;
      case "weight":
        member = member.copyWith(weight: newValue);
        break;
      case "pincode":
        member = member.copyWith(pinCode: newValue);
        break;
      case "relation":
        member = member.copyWith(relation: newValue);
        break;
    }
    return member;
  }

  submitEditingForm() {
    syncApiAndFormValues(
        nextNode: PrePolicyEditNodes.EDIT,
        fullPageLoader: true,
        redirectToNextNode: true);
  }

  /// sync whenever you'll be making api calls
  syncApiAndFormValues(
      {required PrePolicyEditNodes nextNode,
      bool addMemberFlow = false,
      bool redirectToNextNode = false,
      bool fullPageLoader = false,
      bool showErrorScreen = false}) async {
    if (addMemberFlow) editType = EditType.MEMBER;

    emit(ShowFormEditingLoader(fullPageLoader: fullPageLoader));
    prePolicyEditingResponse?.updateFromFormValues(formValues);
    Map<String, dynamic> requestBody =
        prePolicyEditingResponse?.toRequestBody(nextNode.value) ?? {};
    final response =
        await _repo.manageJourney(JourneyType.PRE_POLICY_EDIT, requestBody);

    if (response.error != null) {
      emit(DismissFormEditingLoader(fullPageLoader: fullPageLoader));
      await Future.delayed(Duration(milliseconds: 100));
      if (showErrorScreen)
        emit(Error());
      else
        emit(ErrorToast());
      return;
    }

    prePolicyEditingResponse = response;
    this.nextNode = prePolicyEditingResponse?.getHealthJourneyManagerNode();
    formValues.populateFormValuesFromPrePolicyEditsResponse(
        prePolicyEditingResponse?.edit?.oldValue,
        prePolicyEditingResponse?.edit?.packagePlans,
        prePolicyEditingResponse?.edit?.deductibles,
        paymentDate: prePolicyEditingResponse?.edit?.entityDetails?.paymentDate,
        isOld: true);
    formValues.populateFormValuesFromPrePolicyEditsResponse(
        prePolicyEditingResponse?.edit?.newValue,
        prePolicyEditingResponse?.edit?.packagePlans,
        prePolicyEditingResponse?.edit?.deductibles,
        paymentDate: prePolicyEditingResponse?.edit?.entityDetails?.paymentDate,
        isOld: false);
    formValues.updateMemberStatus();
    formValues.sortNewByOldValues();
    String? familyConstraintsData = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.FAMILY_CONSTRAINS);

    List<Map<String, dynamic>>? parsedList = familyConstraintsData != null
        ? List<Map<String, dynamic>>.from(jsonDecode(familyConstraintsData))
        : null;

    EndorsementFormValidatorConfig? config = parsedList != null
        ? EndorsementFormValidatorConfig.fromJson(parsedList)
        : null;

    formValues
        .setConfig(prePolicyEditingResponse?.edit?.familyConstrains ?? config!);
    emit(DismissFormEditingLoader(fullPageLoader: fullPageLoader));
    await Future.delayed(Duration(milliseconds: 100));
    int totalEdits = formValues.calculateTotalEdits;
    bool enableCta = totalEdits != 0 && formValues.areAllEditsValid;
    PremiumDetails premiumDetails =
        mapToPremiumDetails(prePolicyEditingResponse?.edit?.deltaPremium);

    emit(Loaded(
        formValues: formValues,
        addMemberFlow: addMemberFlow,
        redirectToNextNode: redirectToNextNode,
        amount: premiumDetails.toBePaid?.toInt(),
        enableCta: enableCta,
        totalEdits: totalEdits,
        validationConfig:
            prePolicyEditingResponse?.edit?.familyConstrains ?? config!,
        refreshOverview: true,
        showAddMore: (formValues.newValues?.memberDetails?.length ?? 0) + 1 <
            formValues.getMaxMemberLimit()));
    await Future.delayed(Duration(milliseconds: 200));
    emit(AutoScroll());
  }

  Future<void> removeMember(String insuredId) async {
    editType = EditType.MEMBER;

    emit(Loading());
    await Future.delayed(Duration(milliseconds: 100));

    // Check if the member being removed is the proposer (self-exclusion)
    bool isProposerRemoval =
        formValues.newValues?.proposerDetails?.insuredId == insuredId;

    if (isProposerRemoval) {
      // Handle proposer self-exclusion
      formValues.newValues?.proposerDetails?.isSelfExcluded = true;
      formValues.newValues?.proposerDetails?.isRemoved = true;
    } else {
      // Handle regular member removal
      formValues.newValues?.memberDetails?.forEach((member) {
        if (member?.insuredId == insuredId) {
          member?.isRemoved = true;
        }
      });
    }

    await syncApiAndFormValues(
        nextNode: PrePolicyEditNodes.PREMIUM, showErrorScreen: true);
  }

  Future<void> addMemberBack(String insuredId) async {
    editType = EditType.MEMBER;

    emit(Loading());
    await Future.delayed(Duration(milliseconds: 100));

    // Check if the member being added back is the proposer (self-addition)
    bool isProposerAddition =
        formValues.newValues?.proposerDetails?.insuredId == insuredId;

    if (isProposerAddition) {
      // Handle proposer self-addition back
      formValues.newValues?.proposerDetails?.isSelfExcluded = false;
      formValues.newValues?.proposerDetails?.isRemoved = false;

      /// Find the corresponding insuredNumber from old values
      var oldProposer = formValues.oldValues?.proposerDetails;
      if (oldProposer != null) {
        formValues.newValues?.proposerDetails?.insuredNumber =
            oldProposer.insuredNumber;
      }
    } else {
      // Handle regular member addition back
      formValues.newValues?.memberDetails?.forEach((member) {
        if (member?.insuredId == insuredId) {
          member?.isRemoved = false;

          /// Find the corresponding insuredNumber from old values
          var oldMember = formValues.oldValues?.memberDetails?.firstWhere(
            (oldMember) => oldMember?.insuredId == insuredId,
            orElse: () => null,
          );

          if (oldMember != null) {
            member?.insuredNumber = oldMember.insuredNumber;
          }
        }
      });
    }

    await syncApiAndFormValues(
        nextNode: PrePolicyEditNodes.PREMIUM, showErrorScreen: true);
  }

  List<PickerModel> getAvailableRelationshipList(
      EndorsementFormValidatorConfig config) {
    final currentMembers = formValues.newValues?.memberDetails ?? [];
    currentMembers.add(formValues.newValues?.proposerDetails);
    final relationshipCounts = <String, int>{};

    for (var member in currentMembers) {
      if (member != null && member.relation != null) {
        relationshipCounts[member.relation!] =
            (relationshipCounts[member.relation!] ?? 0) + 1;
      }
    }

    List<PickerModel> availableRelationships = [];

    for (PickerModel pickerModel in _ppeUtils.getRelationList()) {
      bool isAvailable = true;
      for (FamilyConstrains constraint in config.familyConstrains) {
        if (constraint.option?.equalsIgnoreCase(pickerModel.id) ?? false) {
          final currentCount =
              relationshipCounts[pickerModel.id.toLowerCase()] ?? 0;
          if (currentCount >= (constraint.max ?? double.infinity)) {
            isAvailable = false;
          }
          break;
        }
      }

      if (isAvailable) {
        availableRelationships.add(pickerModel);
      }
    }

    return availableRelationships;
  }

  Future<void> addProposerSelfToPolicy({
    required String height,
    required String weight,
  }) async {
    editType = EditType.MEMBER;

    emit(Loading());
    await Future.delayed(Duration(milliseconds: 100));

    // Get proposer details from newValues or oldValues
    MemberDetails? proposerDetails = formValues.newValues?.proposerDetails ??
        formValues.oldValues?.proposerDetails;

    if (proposerDetails == null) {
      emit(ErrorToast());
      return;
    }

    // Use original insured_id or fallback to user_id
    String insuredId = proposerDetails.insuredId?.isNotEmpty == true
        ? proposerDetails.insuredId!
        : proposerDetails.userId ?? '';

    if (insuredId.isEmpty) {
      emit(ErrorToast());
      return;
    }

    // Update proposer details with height and weight
    formValues.newValues?.proposerDetails?.height = height;
    formValues.newValues?.proposerDetails?.weight = weight;
    formValues.newValues?.proposerDetails?.isSelfExcluded = false;
    formValues.newValues?.proposerDetails?.isRemoved = false;

    // Directly add proposer back to insured container with original ID
    // instead of using addInsuredMember which creates a new member
    Map<String, ParameterValue> parameterMap = {};

    // Build parameter map from proposer details
    if (proposerDetails.userId?.isNotEmpty == true) {
      parameterMap['user_id'] = ParameterValue(
          id: null, value: proposerDetails.userId, parameterVersion: null);
    }
    if (proposerDetails.name?.isNotEmpty == true) {
      parameterMap['name'] = ParameterValue(
          id: null, value: proposerDetails.name, parameterVersion: null);
    }
    if (proposerDetails.gender?.isNotEmpty == true) {
      parameterMap['gender'] = ParameterValue(
          id: null, value: proposerDetails.gender, parameterVersion: null);
    }
    if (proposerDetails.dateOfBirth?.isNotEmpty == true) {
      parameterMap['dob'] = ParameterValue(
          id: null, value: proposerDetails.dateOfBirth, parameterVersion: null);
    }
    if (proposerDetails.relation?.isNotEmpty == true) {
      parameterMap['relation'] = ParameterValue(
          id: null, value: proposerDetails.relation, parameterVersion: null);
    }
    if (height.isNotEmpty) {
      parameterMap['height'] = ParameterValue(
          id: null,
          value: int.parse(_ppeUtils.fromInchFeetToAckoHeight(height)),
          parameterVersion: null);
    }
    if (weight.isNotEmpty) {
      parameterMap['weight'] = ParameterValue(
          id: null, value: int.parse(weight), parameterVersion: null);
    }
    if (proposerDetails.pinCode?.isNotEmpty == true) {
      parameterMap['pincode'] = ParameterValue(
          id: null, value: proposerDetails.pinCode, parameterVersion: null);
    }
    if (proposerDetails.mobileNumber?.isNotEmpty == true) {
      parameterMap['phone'] = ParameterValue(
          id: null,
          value: proposerDetails.mobileNumber,
          parameterVersion: null);
    }
    if (proposerDetails.email?.isNotEmpty == true) {
      parameterMap['email'] = ParameterValue(
          id: null, value: proposerDetails.email, parameterVersion: null);
    }

    // Add journey parameter for pre-policy edit
    parameterMap['journey'] = ParameterValue(
        id: null, value: 'pre_policy_edit', parameterVersion: null);

    // Create the insured object
    final proposerInsured = Insured(
      insuredId: insuredId,
      insuredNumber: proposerDetails.insuredNumber,
      parameters: Parameters(parameterMap: parameterMap),
    );

    // Add proposer back to insured container
    prePolicyEditingResponse?.edit?.newValue?.insuredContainer
        ?.insuredMap[insuredId] = proposerInsured;

    // Call the add member API
    Map<String, dynamic>? requestBody = prePolicyEditingResponse
        ?.toRequestBody(PrePolicyEditNodes.ADD_MEMBER.value);

    final response =
        await _repo.manageJourney(JourneyType.PRE_POLICY_EDIT, requestBody!);

    if (response.error != null) {
      emit(ErrorToast());
      return;
    }

    // Update the response and refresh data
    prePolicyEditingResponse = response;
    nextNode = prePolicyEditingResponse?.getHealthJourneyManagerNode();

    // Refresh form values
    formValues.populateFormValuesFromPrePolicyEditsResponse(
        prePolicyEditingResponse?.edit?.oldValue,
        prePolicyEditingResponse?.edit?.packagePlans,
        prePolicyEditingResponse?.edit?.deductibles,
        paymentDate: prePolicyEditingResponse?.edit?.entityDetails?.paymentDate,
        isOld: true);
    formValues.populateFormValuesFromPrePolicyEditsResponse(
        prePolicyEditingResponse?.edit?.newValue,
        prePolicyEditingResponse?.edit?.packagePlans,
        prePolicyEditingResponse?.edit?.deductibles,
        paymentDate: prePolicyEditingResponse?.edit?.entityDetails?.paymentDate,
        isOld: false);
    formValues.updateMemberStatus();
    formValues.sortNewByOldValues();

    // Emit success state to navigate back
    emit(SelfAdditionSuccess());
  }

  triggerTapEvent(HLTrackEvents event) {
    String? groupType =
        prePolicyEditingResponse?.edit?.newValue?.currentPlan?.packageType;
    String? communicationEmail = prePolicyEditingResponse
        ?.edit
        ?.newValue
        ?.usersContainer
        ?.usersMap
        .values
        .firstOrNull
        ?.parameters
        .parameterMap['email']
        ?.value;
    String? insuredId = prePolicyEditingResponse
        ?.edit?.newValue?.usersContainer?.usersMap.values.firstOrNull?.userId;

    triggerTapAnalyticalEventsForPPE(
      trackEvent: event,
      proposalId: proposalId,
      platform: Util.getPlatform(),
      product: "health_retail",
      page: "overview:ppe",
      groupType: groupType,
      communicationEmail: communicationEmail,
      communicationPhone: phoneNumber,
      uniqueId: insuredId,
      journey: "pre-policy-edit",
      // phone: phoneNumber,
      origin: "SUREOS",
    );
  }
}
