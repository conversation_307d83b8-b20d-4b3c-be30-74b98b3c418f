import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/acko_text_config.dart';
import 'package:acko_flutter/feature/endorsement/domain/repository/hl_endorsement_repository.dart';
import 'package:design_module/typography/typography.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/material.dart';

class PpeAdultRemovalRestrictedSheet extends StatelessWidget {
  final VoidCallback? onTap;

  const PpeAdultRemovalRestrictedSheet({
    super.key,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final rcPpeAdultRemovalRestrictedSheet = HlEndorsementRepository()
        .prePolicyEdits
        .view
        ?.bottomsheets
        ?.ppeAdultRemovalRestrictedSheet;
    if (rcPpeAdultRemovalRestrictedSheet == null) {
      return SizedBox.shrink();
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(
          height: 8,
        ),
        Container(
          width: 32,
          height: 4,
          decoration: ShapeDecoration(
            color: color4B4B4B,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        const SizedBox(
          height: 14,
        ),
        HybridImage(
          imageUrl: rcPpeAdultRemovalRestrictedSheet.iconUrl ?? '',
          width: 72,
          fit: BoxFit.fitWidth,
        ),
        const SizedBox(
          height: 14,
        ),
        Padding(
          padding: const EdgeInsets.only(
            left: 20,
            right: 20,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AckoTextConfig.i.headingSmall.text(
                rcPpeAdultRemovalRestrictedSheet.title ?? '',
              ),
              const SizedBox(
                height: 16,
              ),
              AckoTextConfig.i.paragraphMedium.text(
                rcPpeAdultRemovalRestrictedSheet.subtitle ?? '',
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 14,
        ),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.only(
            top: 16,
            left: 20,
            right: 20,
            bottom: 20,
          ),
          decoration: BoxDecoration(
            color: colorFFFFFF,
            boxShadow: [
              BoxShadow(
                color: color36354C.withAlpha(8),
                blurRadius: 8,
                offset: Offset(0, -4),
                spreadRadius: -2,
              )
            ],
          ),
          child: AckoDarkButtonFullWidth(
            text: rcPpeAdultRemovalRestrictedSheet.primaryCtaText ?? "",
            onTap: onTap ?? () {},
          ),
        ),
      ],
    );
  }
}
