import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/endorsement/domain/repository/hl_endorsement_repository.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/overview_bloc/ppe_overview_bloc.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/screens/ppe_self_addition_screen.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';

class CallUsNowPrePolicyEdits extends StatelessWidget {
  const CallUsNowPrePolicyEdits({super.key});

  @override
  Widget build(BuildContext context) {
    final rcPpeCallUsNow =
        HlEndorsementRepository().prePolicyEdits.view?.widgets?.callUsNowPpe;
    if (rcPpeCallUsNow == null || rcPpeCallUsNow.show == false) {
      return SizedBox.shrink();
    }
    return Container(
      margin: EdgeInsets.fromLTRB(20, 24, 20, 0),
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.centerRight,
          end: Alignment.centerLeft,
          colors: [colorE4EFFF, colorFBF2FF],
        ),
        border: Border.all(
          color: colorF8F7FC,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SDUIText(
              value: rcPpeCallUsNow.needToShare ?? '',
              textStyle: "pSmall",
              maxLines: 10,
              textColor: color000000),
          GestureDetector(
            onTap: () {
              context.read<PPEOverviewCubit>().triggerTapEvent(
                  HLTrackEvents.TAP_HEALTH_CALL_US_MEDICAL_DISCLOSURE);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PpeSelfAdditionScreen(),
                ),
              );
              // showModalBottomSheet(
              //     useSafeArea: true,
              //     isScrollControlled: true,
              //     barrierColor: color040222.withOpacity(0.7),
              //     context: context,
              //     backgroundColor: Colors.white,
              //     shape: RoundedRectangleBorder(
              //         borderRadius:
              //             BorderRadius.vertical(top: Radius.circular(24.0))),
              //     builder: (context) {
              //       return Padding(
              //           padding: EdgeInsets.only(
              //               bottom: MediaQuery.of(context).viewInsets.bottom),
              //           child: BlocProvider(
              //             create: (context) =>
              //                 TalkToUsCubit(journey: "pre-policy-edit"),
              //             child: TalkToUsSheet(
              //               title: rcPpeCallUsNow.talkToUsTitle ?? '',
              //             ),
              //           ));
              //     });
            },
            child: SDUIText(
                value: rcPpeCallUsNow.callUsNow,
                textConfiguration: TextConfiguration.colored,
                background: WidgetBackground(color: color000000),
                textColor: colorFFFFFF,
                textStyle: "lXSmall",
                border: WidgetBorder(cornerRadius: 10),
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8)),
          ),
        ],
      ),
    );
  }
}
