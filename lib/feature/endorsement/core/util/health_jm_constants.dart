/// pre policy edits enums
enum EditType { PORTING, POLIC<PERSON>, ME<PERSON><PERSON>, NONE }

enum EditPolicyType { PAYMENT, PORTING, RENEWAL }

enum DraftState {
  PAYMENT_AND_DOC_REQUIRED,
  PAYMENT_AND_DOC_REQUIRED_ACTIVE_MANDATE,
  PAYMENT_AND_DOC_REQUIRED_EXPIRED_MANDATE,
  PAYMENT_REQUIRED,
  PAYMENT_REQUIRED_ACTIVE_MANDATE,
  PAYMENT_REQUIRED_EXPIRED_MANDATE,
  DOCUMENT_REQUIRED,
  REFUND_REQUIRED,
  REFUND_REQUIRED_ACTIVE_MANDATE,
  REFUND_REQUIRED_EXPIRED_MANDATE,
  REFUND_AND_DOC_REQUIRED,
  REFUND_AND_DOC_REQUIRED_ACTIVE_MANDATE,
  REFUND_AND_DOC_REQUIRED_EXPIRED_MANDATE,
  UNDER_REVIEW,
  NONE
}

/// note: it should be HL JM Assets
class HealthJourneyManagerAssets {
  /// pre policy edits assets
  static const String memberIcon =
      "https://marketing.ackoassets.com/images/health/pre-policy-edits/profile-avatar.svg";
  static const String successIcon =
      "https://marketing.ackoassets.com/images/health/pre-policy-edits/circle-success-tick.svg";
  static const String refundIcon =
      "https://marketing.ackoassets.com/images/health/pre-policy-edits/refund.svg";
  static const String childNotCoveredIcon =
      "https://marketing.ackoassets.com/images/health/pre-policy-edits/child-not-covered.svg";
  static const String moneyCoinIcon =
      "https://marketing.ackoassets.com/images/health/ppmc/medical-test/free_tests.svg";
  static const String evaluationRequiredIcon =
      "https://marketing.ackoassets.com/images/health/ppmc/medical-test/evaluation_complete_active.svg";
  static const String removeMemberIcon =
      "https://marketing.ackoassets.com/images/health/pre-policy-edits/error.svg";
  static const String changeNotCoveredIcon =
      "https://marketing.ackoassets.com/images/health/pre-policy-edits/my-policies.svg";
  static const String editNotAllowedIcon =
      "https://marketing.ackoassets.com/images/health/pre-policy-edits/no-edit.svg";
  static const String calendarIcon =
      "https://marketing.ackoassets.com/images/health/pre-policy-edits/calendar.svg";
  static const String uploadIcon =
      "https://marketing.ackoassets.com/images/health/pre-policy-edits/upload-file.svg";
  static const String lightPurpleBgGradient =
      'https://marketing.ackoassets.com/images/health/onboarding/gmc/nux_gradient.png';
}

/// String constatnts

const String re_activate_policy = "Re-activate Policy";
const String DEEP_LINK = "DEEP_LINK";
const String BOTTOM_SHEET = "BOTTOM_SHEET";
const String REDIRECT = "REDIRECT";
const String ppe_mandate_updated =
    'Your new mandate has been created and policy updates submitted';
const String ppe_changes_submitted = 'Your policy changes are submitted';
const String draft_days = "3 days";
const String mandateInfo =
    'You’ve enabled autopay. Smart move for long-term protection and worry-free payments.';
const String addMeToCoverage = "Add me to the coverage";
const String height = "Height";
const String weight = "Weight";
